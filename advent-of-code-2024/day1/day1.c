#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define LINE_SIZE 100
#define TABLE_SIZE 1000

void swap(int* a, int* b);
int partition(int arr[], int low, int high);
void quick_sort(int arr[], int low, int high);

int main() {
    FILE *f;
    f = fopen("puzzle1.txt", "r");

    if (f == NULL) {
        printf("File doesn't exist\n");
        return -1;
    }

    char line[LINE_SIZE];
    int left_n, right_n;
    int left_ns[TABLE_SIZE], right_ns[TABLE_SIZE];
    int counter, i, sum;

    counter = 0;
    while (fgets(line,sizeof(line),f)) {
        if (sscanf(line, "%d %d", &left_n, &right_n) == 2) {
            left_ns[counter] = left_n;
            right_ns[counter] = right_n;
            counter++;
        } else {
            printf("Failed to parse line: %s\n", line);
        }
    }

    sum = 0;
    quick_sort(left_ns, 0, counter - 1);
    quick_sort(right_ns, 0, counter - 1);
    for (i = 0; i < counter; i++) {
        sum += abs(left_ns[i] - right_ns[i]);
    }
    printf("Part 1 - Sum of differences: %d\n", sum);

    int j, occurrences, similarity_score;
    similarity_score = 0;
    for (i = 0; i < counter; i++) {
        occurrences = 0;
        for (j = 0; j < counter; j++) {
            if (left_ns[i] == right_ns[j]) {
                occurrences++;
            }
        }
        similarity_score += left_ns[i] * occurrences;
    }
    printf("Part 2 - Similarity score: %d\n", similarity_score);
    return 0;
}

void swap(int* a, int* b) {
    int t = *a;
    *a = *b;
    *b = t;
}

int partition(int arr[], int low, int high) {
    int pivot = arr[high];
    int i = (low - 1);

    for (int j = low; j <= high - 1; j++) {
        if (arr[j] <= pivot) {
            i++;
            swap(&arr[i], &arr[j]);
        }
    }
    swap(&arr[i + 1], &arr[high]);
    return (i + 1);
}

void quick_sort(int arr[], int low, int high) {
    if (low < high) {
        int pi = partition(arr, low, high);

        quick_sort(arr, low, pi - 1);
        quick_sort(arr, pi + 1, high);
    }
}