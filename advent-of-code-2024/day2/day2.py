safe_reports = 0

file = open("puzzle2.txt", "r")
for l in file:
    line = list(map(int, l.split()))
    if len(line) < 2:
        continue
    increasing = line[0] != line[1] and line[0] < line[1]
    safe = line[0] != line[1]
    times_unsafe = 0
    for i in range(1, len(line)):
        if ((increasing and line[i-1] > line[i]) or (not increasing and line[i-1] < line[i]) 
            or line[i-1] == line[i] or abs(line[i-1] - line[i]) < 1 or abs(line[i-1] - line[i]) > 3):
            safe = False
            times_unsafe += 1
        if times_unsafe > 1:
            break
    if times_unsafe <= 1:
        safe = True
    if safe:
        safe_reports += 1

file.close()

print(f"Part 1 - Safe reports: {safe_reports}")


print(f"Part 2 - Safe reports: {safe_reports}")

# 430
