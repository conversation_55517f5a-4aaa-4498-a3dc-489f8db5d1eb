<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<persistence xmlns="https://jakarta.ee/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence https://jakarta.ee/xml/ns/persistence/persistence_3_1.xsd"
             version="3.0">
    <persistence-unit name="XY-Management" transaction-type="RESOURCE_LOCAL">
        <class>core.model.Client</class>
        <class>core.model.Invoice</class>
        <class>core.model.Price</class>
        <class>core.model.Product</class>
        <class>core.model.Receipt</class>
        <class>core.model.SeatingTable</class>
        <class>core.model.MealTransaction</class>

        <properties>
            <property name="jakarta.persistence.jdbc.url" value="***************************************"/>
            <property name="jakarta.persistence.jdbc.user" value="admin"/>
            <property name="jakarta.persistence.jdbc.password" value="password"/>
            <property name="jakarta.persistence.jdbc.driver" value="org.postgresql.Driver"/>
            <property name="hibernate.hbm2ddl.auto" value="update"/>
        </properties>
    </persistence-unit>
</persistence>