package core.service;

import core.dto.ClientDTO;
import core.model.Client;
import core.repository.ClientRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.List;

public class ClientService {

    private EntityManagerFactory emf;

    public ClientService(EntityManagerFactory emf){
        this.emf = emf;
    }

    public void addClient(String name, int taxNumber, String address, String phoneNumber) {
        EntityManager em = emf.createEntityManager();
        ClientRepository cc = new ClientRepository(em);
        // the transaction
        try {
            em.getTransaction().begin();
            cc.addClient(name, taxNumber, address, phoneNumber);
            em.getTransaction().commit();
        } catch (Exception e) {
            if (em.getTransaction().isActive())
                em.getTransaction().rollback();
            System.err.println("Error in adding client: " + e.toString());
        } finally {
            em.close();
        }
    }

    public List<ClientDTO> getClients() {
        EntityManager em = emf.createEntityManager();
        ClientRepository cc = new ClientRepository(em);
        List<ClientDTO> clientDTOs = new ArrayList<>();
        try {
            List<Client> clients =  cc.getAllClients();
            for(Client c : clients) {
                clientDTOs.add(new ClientDTO(c));
            }
        } finally {
            em.close();
        }
        return clientDTOs;
    }
}
