package core.dto;

import core.model.Client;

public class ClientDTO {

    private String name;

    private int taxNumber;

    private String address;

    private String phoneNumber;

    public ClientDTO (Client client){
        this.name = client.getName();
        this.taxNumber = client.getTaxNumber();
        this.address = client.getAddress();
        this.phoneNumber = client.getPhoneNumber();
    }

    public String getName(){
        return this.name;
    }

    public int getTaxNumber() {
        return this.taxNumber;
    }

    public String getAddress() {
        return this.address;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setTaxNumber(int taxNumber) {
        this.taxNumber = taxNumber;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

}
