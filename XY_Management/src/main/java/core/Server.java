package core;

import core.api.ClientController;
import core.dto.ClientDTO;
import core.service.ClientService;

import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Persistence;

import java.util.List;

import static jakarta.persistence.Persistence.createEntityManagerFactory;

public class Server {

    private ClientController clientController;
    private EntityManagerFactory emf;

    public void run() {
        // Connects to the database
        try {


            emf = Persistence.createEntityManagerFactory("XY-Management");
            clientController = new ClientController(new ClientService(emf));
            // exceptions thrown by JPA are not checked
            System.out.println("adding client");
            clientController.addClient("adeussdsd", 123, "rua", "11223");
            System.out.println("getting client");
            Iterable<ClientDTO> clients = clientController.getClients();
            System.out.println("start to print client");

            for(ClientDTO c : clients ){
                System.out.println(c.getName());
            }


        } catch (Exception e) {
            java.lang.System.err.println("Error connecting database");
        }
    }

    public void stopRun() {
        // Closes the database connection
        emf.close();
    }

    public ClientController getCustomerService() {
        return clientController;
    }
}
