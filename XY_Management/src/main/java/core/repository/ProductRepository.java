package core.repository;

import core.model.Product;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceException;

public class ProductRepository {
    
    private EntityManager em;
    
    public ProductRepository(EntityManager em){
        this.em = em;
    }

    public Product getProduct (String id) {
        try{
            return em.createNamedQuery(Product.FIND_BY_ID, Product.class)
                    .setParameter(Product.ID, id)
                    .getSingleResult();
        } catch (PersistenceException e) {
            /*TODO catch exception*/
            System.err.println("Product not found from database." + e.toString());
            return null;
        }
    }

    public void addProduct (String id, String name, double price, double tax, String type){
        Product p = new Product(id, name, price, tax, type);
        em.persist(p);
    }
}
