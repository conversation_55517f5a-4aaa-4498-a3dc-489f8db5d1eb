package core.repository;

import core.model.SeatingTable;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceException;

public class TableRepository {

    private EntityManager em;

    /**
     * Constructs a table's catalog giving an entity manager factory
     */
    public TableRepository(EntityManager em){
        this.em = em;
    }

    public SeatingTable getTable (int tableNumber) {
        try{
            return em.createNamedQuery(SeatingTable.FIND_BY_TABLE_NUMBER, SeatingTable.class)
                    .setParameter(SeatingTable.TABLE_NUMBER, tableNumber)
                    .getSingleResult();
        } catch (PersistenceException e) {
            /*TODO catch exception*/
            System.err.println("Table not found from database." + e.toString());
            return null;
        }
    }

    public void addTable (int tableNumber, String mealType){
        SeatingTable t = new SeatingTable(tableNumber, mealType);
        em.persist(t);
    }

}
