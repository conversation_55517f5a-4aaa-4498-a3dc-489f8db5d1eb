package core.repository;

import core.model.*;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceException;
import java.util.List;

public class TransactionRepository {

    private EntityManager em;

    public TransactionRepository(EntityManager em){
        this.em = em;
    }

    public MealTransaction getTransaction(int id) {
        try {
            return em.find(MealTransaction.class, id);
        } catch (PersistenceException e) {
            /*TODO catch exception*/
            System.err.println("Transaction not found from database." + e.toString());
            return null;
        }
    }

    public List<MealTransaction> getAllTransactions() {
        try {
            return em.createNamedQuery(MealTransaction.FIND_ALL, MealTransaction.class)
                    .getResultList();
        } catch (PersistenceException e) {
            /*TODO catch exception*/
            System.err.println("Transactions not found from database." + e.toString());
            return null;
        }
    }

    /**
     * Adds a receipt to the database
     * @param seatingTable the table
     * @return the receipt
     */
    public MealTransaction addTransaction(SeatingTable seatingTable) {
        MealTransaction t = null;
        if (seatingTable.getTransaction() != null) {
            try {
                MealTransaction old = em.find(MealTransaction.class, seatingTable.getTransaction().getId());
                old.updateProducts(seatingTable.getProducts(), seatingTable.getTotal());
                t = em.merge(old);
            } catch (PersistenceException e) {
                /*TODO catch exception*/
                System.err.println("Transaction not found from database." + e.toString());
            }
        } else {
            t = new Receipt(seatingTable);
            em.persist(t);
        }
        return t;
    }

    /**
     * Adds an invoice to the database
     * @param seatingTable the table
     * @param client the client
     * @param paymentMethod the payment method
     * @return the invoice
     */
    public MealTransaction addTransaction(SeatingTable seatingTable, Client client, PaymentMethod paymentMethod) {
        MealTransaction t = null;
        if (seatingTable.getTransaction() != null) {
            try {
                MealTransaction old = em.find(MealTransaction.class, seatingTable.getTransaction().getId());
                old.updateProducts(seatingTable.getProducts(), seatingTable.getTotal());
                t = em.merge(old);
            } catch (PersistenceException e) {
                /*TODO catch exception*/
                System.err.println("Transaction not found from database." + e.toString());
            }
        } else {
            t = new Invoice(seatingTable, client, paymentMethod);
            em.persist(t);
        }
        return t;
    }

}
