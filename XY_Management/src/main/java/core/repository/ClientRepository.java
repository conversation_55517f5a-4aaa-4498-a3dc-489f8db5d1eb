package core.repository;

import core.model.Client;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceException;
import java.util.List;


public class ClientRepository {

    private EntityManager em;

    public ClientRepository(EntityManager em){
        this.em = em;
    }

    public Client getClient(int tax) {
        try {
            return em.find(Client.class, tax);
        } catch (PersistenceException e) {
            /*TODO catch exception*/
            System.err.println("Client not found from database." + e.toString());
            return null;
        }
    }

    public List<Client> getAllClients(){
        return em.createNamedQuery(Client.FIND_ALL, Client.class)
                .getResultList();
    }

    public void addClient(String name, int taxNumber, String address, String phoneNumber) {
        if (Client.isValidTaxNumber(taxNumber)) {
            Client c = new Client(name, taxNumber, address, phoneNumber);
            em.persist(c);
        } else {
            System.err.println("Invalid tax number.");
        }
    }
}
