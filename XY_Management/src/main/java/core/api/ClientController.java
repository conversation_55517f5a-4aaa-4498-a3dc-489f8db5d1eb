package core.api;

import core.dto.ClientDTO;
import core.service.ClientService;

public class ClientController {

    private ClientService clientService;

    public ClientController(ClientService clientService){
        this.clientService = clientService;
    }

    public void addClient(String name, int taxNumber, String address, String phoneNumber){
        clientService.addClient(name, taxNumber, address, phoneNumber);
    }

    public Iterable<ClientDTO> getClients(){
        return clientService.getClients();
    }
}
