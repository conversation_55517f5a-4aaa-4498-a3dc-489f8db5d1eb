package core.model;

import jakarta.persistence.*;

@Entity
public class Invoice extends MealTransaction {

    @Enumerated(EnumType.STRING)
    private PaymentMethod paymentMethod;

    @OneToOne @JoinColumn(nullable = false)
    private Client client;

    public Invoice(){}

    public Invoice(SeatingTable seatingTable, Client client, PaymentMethod paymentMethod){
        super(seatingTable);
        this.client = client;
        this.paymentMethod = paymentMethod;
    }

    public PaymentMethod getPaymentMethod(){
        return this.paymentMethod;
    }

    public Client getClient() {
        return client;
    }

    public void setPaymentMethod(PaymentMethod paymentMethod){
        this.paymentMethod = paymentMethod;
    }

    public void setClient(Client client) {
        this.client = client;
    }
}
