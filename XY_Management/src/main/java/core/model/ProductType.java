package core.model;

public enum ProductType {
    APPETIZER("APPETIZER"), //Entradas
    MAIN_COURSE("MAIN_COURSE"), //Prato principal
    BEVERAGE("BEVERAGE"), //<PERSON><PERSON>a
    SIDE_DISH("SIDE_DISH"), //Acompanhamento
    DESSERT("DESSERT"),
    SPECIAL("SPECIAL");

    private String type;

    ProductType(String type) { this.type = type; }

    /** @see java.lang.Object#toString() */
    @Override
    public String toString(){
        return this.type;
    }
}
