package core.model;

import jakarta.persistence.*;

@NamedQuery(name=Client.FIND_ALL, query="SELECT c FROM Client c")
@Entity
public class Client {

    public static final String FIND_ALL = "Client.findAll";

    @Column(nullable = false)
    private String name;

    @Id
    private int taxNumber;

    @Column
    private String address;

    @Column
    private String phoneNumber;

    public Client() {
    }

    public Client (String name, int taxNumber, String address, String phoneNumber){
        this.name = name;
        this.taxNumber = taxNumber;
        this.address = address;
        this.phoneNumber = phoneNumber;
    }

    public String getName(){
        return this.name;
    }

    public int getTaxNumber() {
        return this.taxNumber;
    }

    public String getAddress() {
        return this.address;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setTaxNumber(int taxNumber) {
        this.taxNumber = taxNumber;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    // FIXME: Implement a method that returns true if the tax number is valid, false otherwise
    public static boolean isValidTaxNumber(int taxNumber) {
        return taxNumber > 0;
    }
}

