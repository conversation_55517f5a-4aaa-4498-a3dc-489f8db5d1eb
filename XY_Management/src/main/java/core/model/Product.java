package core.model;

import jakarta.persistence.*;

@Entity
@NamedQuery(name= Product.FIND_BY_ID, query="SELECT c FROM Product c WHERE c.id = :" +
        Product.ID)
public class Product {

    // Named query name constants
    public static final String FIND_BY_ID = "Product.findById";
    public static final String ID = "id";

    @Id
    private String id;

    @Column(nullable = false)
    private String name;

    @OneToOne @JoinColumn(nullable = false)
    private Price price;

    @Enumerated(EnumType.STRING)
    private ProductType type;

    public Product(){}

    public Product(String id, String name, double price, double tax, String type) {
        this.id = id;
        this.name = name;
        this.price = new Price(price, tax);
        this.type = ProductType.valueOf(type);
    }

    public String getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public double getPrice() {
        return this.price.getPriceWithTax();
    }

    public double getTax() {
        return this.price.getTax();
    }

    public String getType() {
        return this.type.toString();
    }
}
