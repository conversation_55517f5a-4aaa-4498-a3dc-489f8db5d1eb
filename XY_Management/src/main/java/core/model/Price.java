package core.model;

import jakarta.persistence.*;

@Entity
public class Price {

    // Product primary key. Needed by JPA.
    @Id @GeneratedValue
    private int id;
    private double priceWithTax;
    private double tax; //IVA already converted to 0.xx ex.: 0.23
    private double discount; //Format 0.xx
    private double priceWithoutDiscount;

    public Price(){}

    public Price(double priceWithTax, double tax) {
        this.priceWithTax = priceWithTax;
        this.tax = tax;
        this.discount = 0.0;
        this.priceWithoutDiscount = priceWithTax;
    }

    public double getPriceWithTax() {
        return this.priceWithTax;
    }

    public double getTax() {
        return this.tax;
    }

    public double getDiscount() {
        return this.discount;
    }

    public double getPriceWithoutDiscount() {
        return this.priceWithoutDiscount;
    }

    public double getDiscounted() {
        return this.priceWithoutDiscount - this.priceWithTax;
    }

    public double getPricePreTax() {
        double taxValue = this.priceWithTax * this.tax;
        return this.priceWithTax - taxValue;
    }

    public void setDiscount(double discount) {
        this.discount = discount;
        this.priceWithTax = this.priceWithoutDiscount * (1.00 - this.discount);
    }

}
