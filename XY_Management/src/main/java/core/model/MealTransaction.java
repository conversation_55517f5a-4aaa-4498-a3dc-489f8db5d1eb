package core.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;

import static jakarta.persistence.InheritanceType.SINGLE_TABLE;

@Entity
@NamedQuery(name= MealTransaction.FIND_ALL, query="SELECT t FROM MealTransaction t")
@Inheritance(strategy = SINGLE_TABLE)
public abstract class MealTransaction {

    public static final String FIND_ALL = "Transaction.findAll";

    @Id @GeneratedValue
    private int id;

    @Column(nullable = false)
    private int tableNumber;

    @Enumerated(EnumType.STRING)
    private MealType mealType;

    @OneToMany(cascade = CascadeType.ALL) @JoinColumn
    private ArrayList<Product> products;

    @Column
    private double total;

    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime createdTime; // of transaction

    public MealTransaction() {
    }

    public MealTransaction(SeatingTable seatingTable) {
        this.tableNumber = seatingTable.getTableNumber();
        this.mealType = seatingTable.getMealType();
        this.products = seatingTable.getProducts();
        this.total = seatingTable.getTotal();
        this.createdTime = LocalDateTime.now();
    }

    public int getId() {
        return this.id;
    }

    public int getTableNumber() {
        return this.tableNumber;
    }

    public MealType getMealType() {
        return this.mealType;
    }

    public ArrayList<Product> getProducts() {
        return this.products;
    }

    public double getTotal() {
        return this.total;
    }

    public LocalDateTime getCreatedTime() {
        return this.createdTime;
    }

    public void updateProducts(ArrayList<Product> products, double total) {
        this.products = products;
        this.total = total;
    }

}
