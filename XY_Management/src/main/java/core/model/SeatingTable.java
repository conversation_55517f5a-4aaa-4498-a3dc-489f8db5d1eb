package core.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;

@Entity
@NamedQuery(name= SeatingTable.FIND_BY_TABLE_NUMBER, query="SELECT t FROM SeatingTable t WHERE t.tableNumber = :" +
        SeatingTable.TABLE_NUMBER)
public class SeatingTable {

    public static final String FIND_BY_TABLE_NUMBER = "Table.findByTableNumber";
    public static final String TABLE_NUMBER = "tableNumber";

    @Id @GeneratedValue
    private int id;
    
    @Column(nullable = false)
    private int tableNumber;
    
    @Enumerated(EnumType.STRING)
    private MealType mealType;

    @OneToMany(cascade = CascadeType.ALL) @JoinColumn
    private ArrayList<Product> products;

    @Column
    private double total;

    @OneToOne @JoinColumn(nullable = false)
    private MealTransaction mealTransaction;

    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime createdTime;
    
    public SeatingTable(){}

    public SeatingTable(int tableNumber, String mealType) {
        this.tableNumber = tableNumber;
        this.mealType = MealType.valueOf(mealType);
        this.products = new ArrayList<>();
        this.total = 0;
        this.mealTransaction = null;
        this.createdTime = LocalDateTime.now();
    }

    public int getTableNumber() { return this.tableNumber; }

    public MealType getMealType(){
        return this.mealType;
    }

    public ArrayList<Product> getProducts(){
        return this.products;
    }

    public double getTotal() {
        return this.total;
    }

    public MealTransaction getTransaction() {
        return this.mealTransaction;
    }

    public LocalDateTime getCreatedTime() {
        return this.createdTime;
    }

    public void setTransaction(MealTransaction mealTransaction) {
        this.mealTransaction = mealTransaction;
    }

    public void setMealType(MealType mealType) {
        this.mealType = mealType;
    }

    public void setProducts(ArrayList<Product> products) {
        this.products = products;
    }

    public void setTotal(double total) {
        this.total = total;
    }

    public void setTableNumber(int tableNumber) {
        this.tableNumber = tableNumber;
    }
}
