***********************************************************
	
		SaleSys @ CSS-LEI-FCUL 
		 
	  GUI Presentation + Domain Model + Data Mapper with JPA
		
***********************************************************


***************
	Overview
***************

This project illustrates 
1) the construction of a GUI with JavaFx  
2) the application of several patterns:
* MVC (https://martinfowler.com/eaaCatalog/modelViewController.html) and the PageController design pattern (see https://martinfowler.com/eaaCatalog/pageController.html)  
* the Domain Model design pattern (see https://martinfowler.com/eaaCatalog/domainModel.html) and Data Mapper(see https://martinfowler.com/eaaCatalog/dataMapper.html) via meta-data for JPA

 
***************
	Use Cases
***************

In this version, we consider there are two use cases: create customer and
process sale. Create customer use case has a single operation to create new customers.
Process sale use case starts with the creation of the sale, after which it is possible to
repeatedly add products to this sale until the sale is closed (ending the use case). 
At any time after the creation  of the sale, it is possible to get the current discount.
 
IMPORTANT: Although the business logic of the use cases is completely implemented,  
the presentation only covers Create Customer.

***************
	Layers
***************

There are four layers: 
* the presentation layer, which contains the code that supports the use of the system by its users through a GUI;
* the application layer, which contains the services provided 
by the application and its role (in this version) is just to provide a facade that hides 
from clients our decision of using the domain model pattern; 
* the business layer, which contains the handlers of use cases, the catalogs and the classes representing the domain objects; 
* the data access layer, with code from JPA provider and other artifacts
automatically generated from the provided meta-data. 


***************
	Tiers
***************

This version of the system has 2 tiers:

The presentation and business components are running in the user machine.
The data tier, with the database stored in a machine with a database server (more precisely, a MySQL server)

***************
	Persistence
***************

The source of data is a MySQL database in a different machine (DB server available in dbserver.alunos.di.fc.ul.pt). In META-INF (src/main/resources) you can find the file 
ersistence.xml with the required data. 

In order to create the database and, then run the project, do the following:

Run dbutils.CreateDatabase that uses JPA properties together with the annotations 
In the code to create the database. (If something is wrong with the annotations the creation 
will fail. You need to fix the annotations and execute the class again.)

Every time you run this class, the tables from the database will be removed
and the database will be created again. The removal and creation of tables 
is based on the current meta-data, so, if there is a change in the meta-data,
classes can be wrongly deleted (because they were created using annotations
that are no longer in place). In this cases remove (manually) the database
and run the dbutils.CreateDatabase again.

Required data for running the system is also load when running this class.
JPA is given the script in META-INF/load-script.sql for insertion of the data. 


***************
	Run the application
***************

In the package client we provide two different clients. One is the simple client 
you should already be familiar with, the other is SimpleGuiClient. Just run it as a 
Java application. As usual, this assumes you have  already created the database and keep
using the VPN.

