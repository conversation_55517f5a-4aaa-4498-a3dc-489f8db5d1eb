<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>pt.ulisboa.ciencias.di</groupId>
	<artifactId>domain-model-jpa-javafx</artifactId>
	<version>2.1</version>
	<packaging>jar</packaging>

	<description>An example of the application of the front controller to the 
  		SaleSys application using JPA implemented using Hibernate and JavaFX
  	</description>

	<properties>
		<!-- Explicitly declaring the source encoding eliminates the following 
			message: -->
		<!-- [WARNING] Using platform encoding (UTF-8 actually) to copy filtered 
			resources, i.e. build is platform dependent! -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

		<!-- <PERSON><PERSON><PERSON> dependency versions -->
		<version.wildfly.maven.plugin>1.0.2.Final</version.wildfly.maven.plugin>

		<!-- Define the version of the JBoss BOMs we want to import to specify 
			tested stacks. -->
		<version.jboss.bom>8.2.1.Final</version.jboss.bom>

		<!-- other plugin versions -->
		<version.compiler.plugin>3.3</version.compiler.plugin>
		<version.surefire.plugin>2.16</version.surefire.plugin>
		<version.war.plugin>2.5</version.war.plugin>

		<!-- maven-compiler-plugin -->
		<maven.compiler.target>1.8</maven.compiler.target>
		<maven.compiler.source>1.8</maven.compiler.source>
	</properties>


	<dependencies>

		<!-- First declare the APIs we depend on and need for compilation. All 
			of them are provided by JBoss WildFly -->

		<!-- Import the JPA API -->
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>5.2.10.Final</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.38</version>
		</dependency>

	</dependencies>
</project>
