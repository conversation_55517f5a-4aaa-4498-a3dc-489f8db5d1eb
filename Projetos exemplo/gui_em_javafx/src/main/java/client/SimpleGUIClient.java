package client;

import facade.exceptions.ApplicationException;
import facade.startup.SaleSys;

/**
 * A simple application client that uses both services.
 *
 * <AUTHOR>
 * @version 1.2 (11/02/2015)
 */
public class SimpleGUIClient {
	
	/**
	 * An utility class should not have public constructors
	 */
	private SimpleGUIClient() {
	}

    /**
     * A simple interaction with the application services
     *
     * @param args Command line parameters
     */
    public static void main(String[] args) {

        SaleSys app = new SaleSys();
        try {
            app.run();
            
            presentation.fx.Startup.startGUI(app);
        } catch (ApplicationException e) {
        	System.out.println("Error: " + e.getMessage());
        	// for debugging purposes only. Typically, in the application
        	// this information can be associated with a "details" button when
        	// the error message is displayed.
        	if (e.getCause() != null)
        		System.out.println("Cause: ");
        	e.printStackTrace();
        }
    }
}