<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>

<GridPane hgap="10.0" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="273.0" prefWidth="484.0" vgap="10.0" xmlns="http://javafx.com/javafx/8.0.111" xmlns:fx="http://javafx.com/fxml/1" fx:controller="presentation.fx.inputcontroller.NewCustomerController">
  <columnConstraints>
    <ColumnConstraints hgrow="SOMETIMES" maxWidth="233.0" minWidth="10.0" prefWidth="133.0" />
    <ColumnConstraints hgrow="SOMETIMES" maxWidth="355.0" minWidth="10.0" prefWidth="341.0" />
  </columnConstraints>
  <rowConstraints>
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
  </rowConstraints>
   <children>
      <Label text="%label.vatNumber" GridPane.halignment="RIGHT" />
      <Label text="Designation:" GridPane.halignment="RIGHT" GridPane.rowIndex="1" />
      <Label text="Phone number:" GridPane.halignment="RIGHT" GridPane.rowIndex="2" />
      <Label text="Discount type:" GridPane.halignment="RIGHT" GridPane.rowIndex="3" />
      <TextField fx:id="vatNumberTextField" GridPane.columnIndex="1">
         <GridPane.margin>
            <Insets right="10.0" />
         </GridPane.margin>
      </TextField>
      <TextField fx:id="designationTextField" GridPane.columnIndex="1" GridPane.rowIndex="1">
         <GridPane.margin>
            <Insets right="10.0" />
         </GridPane.margin>
      </TextField>
      <TextField fx:id="phoneNumberTextField" GridPane.columnIndex="1" GridPane.rowIndex="2">
         <GridPane.margin>
            <Insets right="10.0" />
         </GridPane.margin>
      </TextField>
      <ComboBox fx:id="discountTypeComboBox" onAction="#discountTypeSelected" prefWidth="150.0" promptText="Select a customer discount..." GridPane.columnIndex="1" GridPane.rowIndex="3" />
      <Button mnemonicParsing="false" onAction="#createCustomerAction" text="%button.createCustomer" GridPane.columnIndex="1" GridPane.halignment="RIGHT" GridPane.rowIndex="4">
         <GridPane.margin>
            <Insets right="10.0" />
         </GridPane.margin>
      </Button>
   </children>
</GridPane>
