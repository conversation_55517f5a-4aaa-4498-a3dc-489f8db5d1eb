package dataaccess;

import java.sql.Date;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * A class that allows to transfer tabular data from the data access layer to 
 * the business layer in a way that is independent of the way the data access 
 * layer is implemented. 
 * 
 * Although currently there is single way of populating a TableData from a ResultSet,
 * other alternatives can be easily added.
 * 
 * The advantages of this class in terms of isolation of JDBC come with a price: 
 * all data from the result set is loaded to memory in a eager way.
 * 
 * <AUTHOR>
 * @autjor alopes
 * @version 1.1 (10/03/2018)
 *
 */
public class TableData implements Iterable<TableData.Row> {

	private List<Row> data;
	 
	public static class Row {
		private Map<String, Object> record;
		
		public Row () {
			this.record = new HashMap<>();
		}
		
		public void update(String key, Object value) {
			record.put(key, value);
		}
		
		public String getString(String key) throws PersistenceException {
			try {
				return (String) record.get(key);
			} catch (ClassCastException e) {
				throw new PersistenceException("This value is not a string", e);
			}
		}

		public int getInt(String key) throws PersistenceException {
			try {
				return (int) record.get(key);
			} catch (ClassCastException e) {
				throw new PersistenceException("This value is not a int", e);
			}
		}

		public Date getDate(String key) throws PersistenceException {
			try {
				return (Date) record.get(key);
			} catch (ClassCastException e) {
				throw new PersistenceException("This value is not a data", e);
			}
		}

		public double getDouble(String key) throws PersistenceException {
			try {
				return (double) record.get(key);
			} catch (ClassCastException e) {
				throw new PersistenceException("This value is not a double", e);
			}
		}

	}
	
	/**
	 * @param rs The result set from which the data must be loaded
	 * @return A tabledata object with all data available in the result set rs. 
	 * @throws PersistenceException When there is any problem reading the result set
	 */
	public TableData populate(ResultSet rs) throws PersistenceException {
		try {
			data = new LinkedList<>();
			while(rs.next()) {
				ResultSetMetaData rsmd = rs.getMetaData();
				Row r = new Row();
				for (int i = 1; i <= rsmd.getColumnCount(); i++)  {
					String colName = rsmd.getColumnLabel(i);
					r.update(colName, rs.getObject(colName));
				}
				data.add(r);
			}	
			return this;
		} catch (SQLException e) {
			throw new PersistenceException("Error login result set into memory", e);
		} 
	}

	/**
	 * @return whether this tabledata contains any row.
	 */
	public boolean isEmpty() {
		return data.isEmpty();
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Iterable#iterator()
	 */
	@Override
	public Iterator<Row> iterator() {
		return data.iterator();
	}
		
}
