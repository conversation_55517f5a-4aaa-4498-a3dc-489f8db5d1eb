package dataaccess;

import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import dataaccess.TableData.Row;
import datatypes.SaleStatus;

/**
 * Table Data Gateway for the sales's table
 * 
 * Remarks:
 * 1. See notes in class CustomerTableGateway 
 *  
 * 2. When creating a sale, the newSale method returns the id of the
 * sale just added, so we can add products to the brand new sale. Otherwise,
 * it would not be possible to determine to which sale to add products. 
 * Keep an attribute (in this class) to represent the current sale is not
 * a good solution for many reasons, namely, there can be more than one
 * open sale, and, in case we want to keep a session with the user so 
 * we can isolate the underway sales, the responsibility of keeping the
 * "current sale" should be the responsibility of the class handling the
 * session and not of this class.
 * 
 * 3. The usage of ResultSets is a common practice in this kind of 
 * database interface, but it has the drawback of not being typed and
 * the compiler cannot emit error messages during the compilation phase, 
 * like field type mismatches, unknown field names. All this errors,
 * in case they exist, will can during runtime (hopefully during the
 * testing phase). In most cases it give rises to very boring testing 
 * sessions with errors popping one after the other...
 *  
 * <AUTHOR>
 * @version 1.1 (5/10/2015)
 *
 */
public class SaleTableDataGateway extends TableDataGateway {

    // Database schema constants

    /**
     * Table name
     */
    private static final String TABLE_NAME = "sale";

    /**
	 * Column labels
	 */
	private static final String ID_COLUMN_NAME = "ID";
	private static final String DATE_COLUMN_NAME = "DATE";
	private static final String TOTAL_COLUMN_NAME = "TOTAL";
	private static final String DISCOUNT_COLUMN_NAME = "DISCOUNT_TOTAL";
	private static final String STATUS_COLUMN_NAME = "STATUS";
	private static final String CUSTOMER_ID_COLUMN_NAME = "CUSTOMER_ID";

	/**
	 * A pair of constants to map the status of a sale to a string
	 */
	private static final String CLOSED = "C";
	private static final String OPEN = "O";

	
	// SQL statements
	
    /**
	 * insert a sale
	 */
    private static final String INSERT_SALE_SQL = " ";		// TODO

	/**
	 * obtain a sale by Id SQL statement
	 */
	private static final String GET_SALE_SQL = " ";		// TODO


	/**
	 * Creates a sale table data gateway for given a data source. 
	 * 
	 * @param dataSource The data source the customer gateway interfaces with
	 */
    SaleTableDataGateway(DataSource dataSource) {
		super(dataSource);
	}


    // 1. interaction with the repository (an SQL database) using JDBC

	/**
	 * Add a new sale to the database
	 * 
	 * @param date The date the sale occurred 
	 * @param saleTotal The total sale amount
	 * @param discountTotal The total discount of the sale
	 * @param saleStatus The sale status 
	 * @param customerId The customer id of the sale
	 * @return The sale id just created
	 * @throws PersistenceException In case an internal database error occurs
	 */
	public int insert (java.util.Date date, double saleTotal, double discountTotal, 
			SaleStatus saleStatus, int customerId) throws PersistenceException {
		// TODO: program me
		return 0;
	}


	/**
	 * Gets a sale by its id 
	 * 
	 * @param saleId The sale id to search for
	 * @return The result set with information about the sale
	 * @throws PersistenceException In case there is an error accessing the database.
	 */
	public TableData find (int saleId) throws PersistenceException {
		// TODO
		return null;
	}

	
	// 2. Decode a customer row	
	
	/**
	 * Reads from a result set the value of the corresponding column
	 * @param r The row to read from 
	 * @return the conversion of the value read from the result set 
	 * @throws PersistenceException When there is an error in the reading or conversion
	 */
	public int readId(Row r) throws PersistenceException{
		return r.getInt(ID_COLUMN_NAME);
	}

	/**
	 * Reads from a result set the value of the corresponding column
	 * @param r The row to read from 
	 * @return the conversion of the value read from the result set 
	 * @throws PersistenceException When there is an error in the reading or conversion
	 */
	public Date readDate(Row r) throws PersistenceException{
		return r.getDate(DATE_COLUMN_NAME);
	}

	/**
	 * Reads from a result set the value of the corresponding column
	 * @param r The row to read from 
	 * @return the conversion of the value read from the result set 
	 * @throws PersistenceException When there is an error in the reading or conversion
	 */
	public double readTotal(Row r) throws PersistenceException{
		return r.getDouble(TOTAL_COLUMN_NAME);
	}

	/**
	 * Reads from a result set the value of the corresponding column
	 * @param r The row to read from 
	 * @return the conversion of the value read from the result set 
	 * @throws PersistenceException When there is an error in the reading or conversion
	 */
	public double readDiscount(Row r) throws PersistenceException{
		return r.getDouble(DISCOUNT_COLUMN_NAME);
	}

	/**
	 * Reads from a result set the value of the corresponding column
	 * @param r The row to read from 
	 * @return the conversion of the value read from the result set 
	 * @throws PersistenceException When there is an error in the reading or conversion
	 */
	public SaleStatus readStatus(Row r) throws PersistenceException{
		String status = r.getString(STATUS_COLUMN_NAME);
		if (status.equals(OPEN))
			return SaleStatus.OPEN;
		else if (status.equals(CLOSED))
			return SaleStatus.CLOSED;
		else
			throw new PersistenceException("Internal error reading sale status");
	}
	
	/**
	 * Reads from a result set the value of the corresponding column
	 * @param r The row to read from 
	 * @return the conversion of the value read from the result set 
	 * @throws PersistenceException When there is an error in the reading or conversion
	 */
	public int readCustomerId(Row r) throws PersistenceException{
		return r.getInt(CUSTOMER_ID_COLUMN_NAME);
	}

}
