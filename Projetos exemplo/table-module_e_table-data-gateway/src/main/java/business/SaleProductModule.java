package business;


import dataaccess.Persistence;
import dataaccess.PersistenceException;
import dataaccess.SaleProductTableDataGateway;
import dataaccess.TableData.Row;
import facade.exceptions.ApplicationException;

/**
 * A table module for the products of a sale.
 * See remarks in the Customer class.
 * 
 * <AUTHOR>
 * @version 1.1 (6/11/2016)
 *
 */
public class SaleProductModule extends TableModule {

	private SaleProductTableDataGateway table;

	/**
	 * Constructs a saleproduct module given the persistence repository
	 * 
	 * @param persistence The persistence repository
	 */
	public SaleProductModule (Persistence persistence) {
		super(persistence);
		table = persistence.saleProductTableGateway;
	}

	/**
	 * Add a product to sale.
	 * 
	 * @param saleId The sale id to add a product to
	 * @param productId The id of the product to add to the sale
	 * @param qty The quantity sold of the product
	 * @throws ApplicationException When some internal error occurred while saving the data.
	 */
	public void addProductToSale(int saleId, int productId, double qty) throws ApplicationException {
		try {
			table.insert(saleId, productId, qty);
		} catch (PersistenceException e) {
			throw new ApplicationException("Internal error with selling product id " + productId, e);
		}		
	}

	/**
	 * @param saleId The sale id to retrieve the sale products total 
	 * @return The total of the products sold in a sale
	 * @throws ApplicationException When some internal error occurred while retrieving the data
	 */
	public double getTotal(int saleId) throws ApplicationException {
		//TODO: program me
		return 0;
	}
	
	/**
	 * @param saleId The sale id to retrieve the total of eligible for discount sale products 
	 * @return The total of the products sold in a sale that are eligible for discount
	 * @throws ApplicationException When some internal error occurred while retrieving the data
	 */
	public double getEligibleTotal(int saleId) throws ApplicationException {
		try {
			ProductModule product = new ProductModule(persistence);
			double saleEligibleTotal = 0;
			for (Row r : table.find(saleId)) {
				int productId = table.readProductId(r);
				if (product.isEligibleForDiscount(productId))
					saleEligibleTotal +=  table.readQuantity(r) * 
						product.getFaceValue(table.readProductId(r));
			}
			return saleEligibleTotal;
		} catch (PersistenceException e) {
			throw new ApplicationException("Internal error while getting sale product of sale with id " + saleId, e);			
		}
	}
}