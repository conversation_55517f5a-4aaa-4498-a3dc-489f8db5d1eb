package business;

import dataaccess.Persistence;
import dataaccess.PersistenceException;
import dataaccess.SaleTableDataGateway;
import dataaccess.TableData;

import facade.exceptions.ApplicationException;

public class SaleModule extends TableModule {

	private static final String INTERNAL_ERROR_WITH_SELLING_PRODUCT_ID = "Internal error with product id ";

	private SaleTableDataGateway table;

	/**
	 * Constructs a sale module given the persistence repository
	 * 
	 * @param persistence The persistence repository
	 */
	public SaleModule (Persistence persistence) {
		super(persistence);
		table = persistence.saleTableGateway;
	}

	/**
	 * Add a new sale.
	 * Notice the interaction with the Customer module. We use the
	 * customer module to get the customer id of the customer with
	 * a given VAT number. 
	 * 
	 * @param vat The VAT number of the customer the sale belongs to
	 * @return The internal sale id so that products may be added to the sale
	 * @throws ApplicationException When there is no customer with the given
	 * VAT number or when there is an unexpected error add the sale.
	 */
	public int newSale (int vat) throws ApplicationException {
		// TODO: program me!
		return 0;
	}

	/**
	 * @param saleId The sale id to check if it is closed
	 * @return If the sale is closed
	 * @throws ApplicationException When the sale does not exist or some obscure
	 * error has occurred.
	 */
	public boolean isClosed(int saleId) throws ApplicationException {
		// TODO: program me!
		return false;
	}

	/**
	 * Add a product to a sale.
	 * Notice the usage of the Product module to get the product id from the product code
	 * and to further update its stock existence.
	 * 
	 * @param saleId The sale id to add a product to
	 * @param productCode The product to be added to the sale
	 * @param qty The quantity sold of the product
	 * @throws ApplicationException When the sale is closed, the product code does not
	 * exist or some internal error occurred while saving the data.
	 */
	public void addProductToSale(int saleId, int productCode, double qty) 
			throws ApplicationException {
		// Business rule: products can be only added to open sales
		if (isClosed (saleId))
			throw new ApplicationException("Cannot add products to a closed sale.");		
		int productId = 0;
		try {
			//execute as a unit: decrease stock and add to the sale 
			persistence.beginTransaction();
			// TODO: program me!
			persistence.commit();
		} catch (PersistenceException e) {
			try {
				persistence.rollback();
			} catch (PersistenceException e1) {
				throw new ApplicationException(INTERNAL_ERROR_WITH_SELLING_PRODUCT_ID + productId, e1);			
			}
			throw new ApplicationException(INTERNAL_ERROR_WITH_SELLING_PRODUCT_ID + productId, e);			
		} /*catch (ApplicationException e) {
			try {
				persistence.rollback();
			} catch (PersistenceException e1) {
				throw new ApplicationException(INTERNAL_ERROR_WITH_SELLING_PRODUCT_ID + productId, e1);			
			}
			throw e;			
		}*/
	}

	/**
	 * @param saleId The sale to get the customer id from
	 * @return The customer id of the sale
	 * @throws ApplicationException In case an error occurs when retrieving the
	 * information from the data layer.
	 */
	public int getCustomerId (int saleId) throws ApplicationException {
		// TODO: program me!
		return 0;
	}

	/**
	 * @param saleId The sale to compute the discount.
	 * @return Compute the discount of the sale. 
	 * @throws ApplicationException When some referential integrity problem occurs. This
	 * might happen if a foreign key is not found, for instance.
	 */
	public double getSaleDiscount (int saleId) throws ApplicationException {
		// If the sale is closed, the discount is already computed
		if (isClosed (saleId)) 
			return getDiscount(saleId);
		// compute the sale discount
		return computeDiscount(saleId);
	}

	/**
	 * @param saleId The sale id to compute the discount
	 * @param td The result set containing the information about the product of the sale
	 * @return The discount based on the customer information
	 * @throws ApplicationException If the customer does not exist or some obscure problem occurs. 
	 */
	private double computeDiscount(int saleId) throws ApplicationException  {
		CustomerModule customer = new CustomerModule(persistence);
		switch (customer.getDiscountType(getCustomerId(saleId))) {
		case SALE_AMOUNT:
			return discountOnSaleAmount (saleId);
		case ELIGIBLE_PRODUCTS:
			return discountOnEligibleProducts (saleId);
		default:
			return 0;
		}
	}


	/**
	 * Computes the type 1 discount
	 * @param rs The result set with the sold products 
	 * @return The discount value
	 * @throws ApplicationException When some unexpected error occurs.
	 */
	private double discountOnSaleAmount(int saleId) throws ApplicationException { 
		SaleProductModule saleProduct = new SaleProductModule(persistence);
		double saleTotal = saleProduct.getTotal(saleId);
		ApplicationSettingsModule appSettings = new ApplicationSettingsModule(persistence);
		return saleTotal > appSettings.getAmountThreshold() ? 
				saleTotal * appSettings.getAmountThresholdPercentage() : 0;
	}

	/**
	 * Computes the type 2 discount
	 * @param td The result set with the sold products 
	 * @return The discount value
	 * @throws ApplicationException When some unexpected error occurs.
	 */
	private double discountOnEligibleProducts(int saleId) throws ApplicationException {
		SaleProductModule saleProduct = new SaleProductModule(persistence);
		ApplicationSettingsModule appSettings = new ApplicationSettingsModule(persistence);
		return saleProduct.getEligibleTotal(saleId) * appSettings.getEligiblePercentage();
	}


	/**
	 * @param saleId The sale id to obtain the discount amount. When the sale is closed
	 * the discount is computed and stored in an attribute so it need not to be computed 
	 * every time. 
	 * @return The discount of the sale (when closed)
	 * @throws ApplicationException When some persistence error occurs.
	 */
	private double getDiscount(int saleId) throws ApplicationException {
		try {
			TableData td = getSale(saleId);
			return table.readDiscount(td.iterator().next());
		} catch (PersistenceException e) {
			throw new ApplicationException("Internal error obtaining the discount for sale with id " + saleId, e);
		}
	}


	/**
	 * A service method to get a sale by id.
	 * 
	 * @param saleId The sale id of the sale to be fetched
	 * @return The result set with the sale matching the sale id
	 * @throws ApplicationException When the sale does not exist or some 
	 * error occurs.
	 * @ensures result!=null && !result.isEmpty()
	 */
	private TableData getSale(int saleId) throws ApplicationException {
		// TODO: program me!
		return null;
	}
}
