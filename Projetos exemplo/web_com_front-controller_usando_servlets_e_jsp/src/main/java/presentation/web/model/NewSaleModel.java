package presentation.web.model;

import application.SaleService;

/**
 * Model class to assist in the response of a new customer action.
 * This class is the response information expert.
 * 
 * <AUTHOR>
 *
 */
public class NewSaleModel extends Model {

	private String vatNumber;
	private SaleService saleService;
		
	public String getVATNumber() {
		return vatNumber;
	}
	
	public void setVATNumber(String vatNumber) {
		this.vatNumber = vatNumber;
	}
		
	public void clearFields() {
		vatNumber = "";
	}

	public void setCurrentSaleService(SaleService saleService) {
		this.saleService = saleService;		
	}
	
	public String getDesignation() {
		return saleService == null ? "" : saleService.getCustomerDesignation();
	}
}
