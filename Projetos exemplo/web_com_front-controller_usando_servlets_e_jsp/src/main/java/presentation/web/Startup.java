package presentation.web;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import facade.exceptions.ApplicationException;
import facade.startup.SaleSys;

/**
 * Application Lifecycle Listener implementation class Leasing
 *
 */
@WebListener
public class Startup implements ServletContextListener {

	/**
     * @see ServletContextListener#contextInitialized(ServletContextEvent)
     */
    public void contextInitialized(ServletContextEvent event)  { 
    	SaleSys app = new SaleSys();
    	try {
			app.run();
		} catch (ApplicationException e) {
			e.printStackTrace();
		}
        event.getServletContext().setAttribute("app", app);
    }

    /**
     * @see ServletContextListener#contextDestroyed(ServletContextEvent)
     */
    public void contextDestroyed(ServletContextEvent event)  {
        SaleSys app = (SaleSys) event.getServletContext().getAttribute("app");
        app.stopRun();
    }

}
