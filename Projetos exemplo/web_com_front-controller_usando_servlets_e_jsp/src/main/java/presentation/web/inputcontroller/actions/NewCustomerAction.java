package presentation.web.inputcontroller.actions;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import facade.startup.SaleSys;
import presentation.web.model.NewCustomerModel;


/**
 * Handles the novo cliente event
 * 
 * <AUTHOR>
 *
 */
public class NewCustomerAction extends Action {
	
	@Override
	public void process(HttpServletRequest request, HttpServletResponse response) 
			throws ServletException, IOException {
		SaleSys app = (SaleSys) request.getServletContext().getAttribute("app");

		NewCustomerModel model = new NewCustomerModel();
		model.setCustomerService(app.getCustomerService());
		request.setAttribute("model", model);
		request.getRequestDispatcher("/addCustomer/newCustomer.jsp").forward(request, response);
	}

}
