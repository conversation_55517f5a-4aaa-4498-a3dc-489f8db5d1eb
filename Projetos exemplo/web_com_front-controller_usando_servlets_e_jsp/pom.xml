<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>pt.ulisboa.ciencias.di</groupId>
	<artifactId>domain-model-jpa-web-v0</artifactId>
	<version>2.1</version>
	<packaging>war</packaging>

	<description>An example of a web application</description>

	<properties>
		<!-- Explicitly declaring the source encoding eliminates the following 
			message: -->
		<!-- [WARNING] Using platform encoding (UTF-8 actually) to copy filtered 
			resources, i.e. build is platform dependent! -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

		<!-- J<PERSON><PERSON> dependency versions -->
		<version.wildfly.maven.plugin>1.0.2.Final</version.wildfly.maven.plugin>

		<!-- Define the version of the JBoss BOMs we want to import to specify 
			tested stacks. -->
		<version.jboss.bom>8.2.1.Final</version.jboss.bom>

		<!-- other plugin versions -->
		<version.compiler.plugin>3.3</version.compiler.plugin>
		<version.surefire.plugin>2.16</version.surefire.plugin>
		<version.war.plugin>2.5</version.war.plugin>

		<!-- maven-compiler-plugin -->
		<maven.compiler.target>1.8</maven.compiler.target>
		<maven.compiler.source>1.8</maven.compiler.source>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- JBoss distributes a complete set of Java EE 7 APIs including a Bill 
				of Materials (BOM). A BOM specifies the versions of a "stack" (or a collection) 
				of artifacts. We use this here so that we always get the correct versions 
				of artifacts. Here we use the jboss-javaee-7.0-with-tools stack (you can 
				read this as the JBoss stack of the Java EE 7 APIs, with some extras tools 
				for your project, such as Arquillian for testing) and the jboss-javaee-7.0-with-hibernate 
				stack you can read this as the JBoss stack of the Java EE 7 APIs, with extras 
				from the Hibernate family of projects) -->
			<dependency>
				<groupId>org.wildfly.bom</groupId>
				<artifactId>jboss-javaee-7.0-with-tools</artifactId>
				<version>${version.jboss.bom}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.wildfly.bom</groupId>
				<artifactId>jboss-javaee-7.0-with-hibernate</artifactId>
				<version>${version.jboss.bom}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		
		<!-- Import needed for locally running CreateDatabase (org.hibernate.ejb.HibernatePersistence)
			Should be eliminated once the database is created, since wildfly provides it already. 
			Do not forget of running maven update project after any schange-->
       <!-- 
       <dependency>
	        <groupId>org.hibernate</groupId>
	        <artifactId>hibernate-entitymanager</artifactId>
	        <version>4.3.7.Final</version>
        </dependency>	
		-->
		
		<!-- Declare the APIs we depend on and need for compilation. All 
			of them are provided by JBoss WildFly -->

		<!-- Import the JPA API -->
		<dependency>
			<groupId>org.hibernate.javax.persistence</groupId>
			<artifactId>hibernate-jpa-2.1-api</artifactId>
			<scope>provided</scope>
		</dependency>

		
		<!-- Import the Servlet API -->
		<dependency>
			<groupId>org.jboss.spec.javax.servlet</groupId>
			<artifactId>jboss-servlet-api_3.1_spec</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- Import the JSTL API -->
		<dependency>
			<groupId>org.jboss.spec.javax.servlet.jstl</groupId>
			<artifactId>jboss-jstl-api_1.2_spec</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.38</version>
        </dependency>

	</dependencies>
	<build>
		<!-- Maven will append the version to the finalName (which is the name 
			given to the generated war, and hence the context root) -->
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<artifactId>maven-war-plugin</artifactId>
				<version>${version.war.plugin}</version>
				<configuration>
					<failOnMissingWebXml>false</failOnMissingWebXml>
				</configuration>
			</plugin>
			<!-- The WildFly plugin deploys your war to a local WildFly container -->
			<!-- To use, run: mvn package wildfly:deploy -->
			<plugin>
				<groupId>org.wildfly.plugins</groupId>
				<artifactId>wildfly-maven-plugin</artifactId>
				<version>${version.wildfly.maven.plugin}</version>
			</plugin>
		</plugins>
	</build>
</project>
