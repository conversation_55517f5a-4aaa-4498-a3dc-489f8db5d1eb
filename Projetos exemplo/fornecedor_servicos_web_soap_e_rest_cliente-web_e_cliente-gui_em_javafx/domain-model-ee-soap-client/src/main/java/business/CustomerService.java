
package business;

import java.util.List;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "CustomerService", targetNamespace = "http://business/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface CustomerService {


    /**
     * 
     * @return
     *     returns java.util.List<business.Discount>
     * @throws ApplicationException_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "getDiscounts", targetNamespace = "http://business/", className = "business.GetDiscounts")
    @ResponseWrapper(localName = "getDiscountsResponse", targetNamespace = "http://business/", className = "business.GetDiscountsResponse")
    public List<Discount> getDiscounts()
        throws ApplicationException_Exception
    ;

    /**
     * 
     * @param arg3
     * @param arg2
     * @param arg1
     * @param arg0
     * @return
     *     returns business.CustomerDTO
     * @throws ApplicationException_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "addCustomer", targetNamespace = "http://business/", className = "business.AddCustomer")
    @ResponseWrapper(localName = "addCustomerResponse", targetNamespace = "http://business/", className = "business.AddCustomerResponse")
    public CustomerDTO addCustomer(
        @WebParam(name = "arg0", targetNamespace = "")
        int arg0,
        @WebParam(name = "arg1", targetNamespace = "")
        String arg1,
        @WebParam(name = "arg2", targetNamespace = "")
        int arg2,
        @WebParam(name = "arg3", targetNamespace = "")
        int arg3)
        throws ApplicationException_Exception
    ;

}
