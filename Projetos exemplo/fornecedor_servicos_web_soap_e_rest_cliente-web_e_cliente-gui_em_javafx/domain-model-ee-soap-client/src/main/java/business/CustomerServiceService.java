
package business;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "CustomerServiceService", targetNamespace = "http://business/", wsdlLocation = "http://localhost:8080/domain-model-ee-business/CustomerService?wsdl")
public class CustomerServiceService
    extends Service
{

    private final static URL CUSTOMERSERVICE_WSDL_LOCATION;
    private final static WebServiceException CUSTOMERSERVICE_EXCEPTION;
    private final static QName CUSTOMERSERVICE_QNAME = new QName("http://business/", "AddCustomerHandlerService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://localhost:8080/domain-model-ee-business/AddCustomerHandler?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        CUSTOMERSERVICE_WSDL_LOCATION = url;
        CUSTOMERSERVICE_EXCEPTION = e;
    }

    public CustomerServiceService() {
        super(__getWsdlLocation(), CUSTOMERSERVICE_QNAME);
    }

    public CustomerServiceService(WebServiceFeature... features) {
        super(__getWsdlLocation(), CUSTOMERSERVICE_QNAME, features);
    }

    public CustomerServiceService(URL wsdlLocation) {
        super(wsdlLocation,CUSTOMERSERVICE_QNAME);
    }

    public CustomerServiceService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, CUSTOMERSERVICE_QNAME, features);
    }

    public CustomerServiceService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public CustomerServiceService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns CustomerService
     */
    @WebEndpoint(name = "CustomerServicePort")
    public CustomerService getAddCustomerHandlerPort() {
        return super.getPort(new QName("http://business/", "AddCustomerHandlerPort"), CustomerService.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns AddCustomerHandler
     */
    @WebEndpoint(name = "CustomerServicePort")
    public CustomerService getAddCustomerHandlerPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://business/", "CustomerServicePort"), CustomerService.class, features);
    }

    private static URL __getWsdlLocation() {
        if (CUSTOMERSERVICE_EXCEPTION!= null) {
            throw CUSTOMERSERVICE_EXCEPTION;
        }
        return CUSTOMERSERVICE_WSDL_LOCATION;
    }

}
