package presentation.cli;

import business.ApplicationException_Exception;
import business.CustomerService;
import business.CustomerServiceService;

/**
 * A simple application client that uses both services.
 *	
 * <AUTHOR>
 * @version 1.2 (11/02/2015)
 * 
 */
public class SimpleSOAPClient {

	/**
	 * A simple interaction with the application services
	 * 
	 * @param args Command line parameters
	 */
	public static void main(String[] args) {
		
		// Make a service
	    CustomerServiceService service = new CustomerServiceService();

	    // Now use the service to get a stub which implements the SDI.
	    CustomerService customerService = service.getAddCustomerHandlerPort();

	    // Make the actual call
	    try {
			customerService.addCustomer(168027852, "Customer 1", 217500255, 1);
			System.out.println("Cliente adiciondo com sucesso.");
		} catch (ApplicationException_Exception e) {
			System.out.println("Erro ao adicionar cliente.");
			System.out.println("Causa:");
			e.printStackTrace();
		}

	}
}
