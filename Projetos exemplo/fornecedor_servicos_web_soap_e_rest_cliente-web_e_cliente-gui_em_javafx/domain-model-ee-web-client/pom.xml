<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>pt.ulisboa.ciencias.di</groupId>
		<artifactId>domain-model-ee</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>domain-model-ee-web-client</artifactId> 
	<packaging>war</packaging>

	<name>domain-model-ee-web-client</name>

	<dependencies>

		<!-- Dependency on the EJB module so we can use its services if needed -->
		<dependency>
			<groupId>pt.ulisboa.ciencias.di</groupId>
			<artifactId>domain-model-ee-business</artifactId>
			<type>ejb-client</type>
			<scope>provided</scope>
		</dependency>

		<!-- Import the EJB API -->
		<dependency>
			<groupId>org.jboss.spec.javax.ejb</groupId>
			<artifactId>jboss-ejb-api_3.2_spec</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- provides servlets -->
		<dependency>
			<groupId>org.jboss.spec.javax.servlet</groupId>
			<artifactId>jboss-servlet-api_3.1_spec</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- Import the JSTL API -->
		<dependency>
			<groupId>org.jboss.spec.javax.servlet.jstl</groupId>
			<artifactId>jboss-jstl-api_1.2_spec</artifactId>
			<scope>provided</scope>
		</dependency>

	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<artifactId>maven-war-plugin</artifactId>
				<version>${version.war.plugin}</version>
				<configuration>
					<!-- Java EE 7 doesn't require web.xml, Maven needs to catch up! -->
					<failOnMissingWebXml>false</failOnMissingWebXml>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
