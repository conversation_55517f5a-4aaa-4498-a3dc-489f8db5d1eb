<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>pt.ulisboa.ciencias.di</groupId>
		<artifactId>domain-model-ee</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>domain-model-ee-ear</artifactId>
	<packaging>ear</packaging>

	<name>domain-model-ee-ear</name>

	<dependencies>

		<!-- Depend on the ejb module and war so that we can package them -->
		<dependency>
			<groupId>pt.ulisboa.ciencias.di</groupId>
			<artifactId>domain-model-ee-web-client</artifactId>
			<type>war</type>
		</dependency>

		<dependency>
			<groupId>pt.ulisboa.ciencias.di</groupId>
			<artifactId>domain-model-ee-business</artifactId>
			<type>ejb</type>
		</dependency>

		<dependency>
			<groupId>pt.ulisboa.ciencias.di</groupId>
			<artifactId>domain-model-ee-rest-client</artifactId>
			<type>war</type>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-ear-plugin</artifactId>
				<version>${version.ear.plugin}</version>
				<configuration>
					<!-- Tell Maven we are using Java EE 7 -->
					<version>6</version>
					<!-- Use Java EE ear libraries as needed. Java EE ear libraries are 
						in easy way to package any libraries needed in the ear, and automatically 
						have any modules (EJB-JARs and WARs) use them -->
					<defaultLibBundleDir>lib</defaultLibBundleDir>
					<modules>
						<webModule>
							<groupId>pt.ulisboa.ciencias.di</groupId>
							<artifactId>domain-model-ee-web-client</artifactId>
							<contextRoot>/domain-model-ee-web-client</contextRoot>
						</webModule>
						<webModule>
							<groupId>pt.ulisboa.ciencias.di</groupId>
							<artifactId>domain-model-ee-rest-client</artifactId>
							<contextRoot>/domain-model-ee-rest</contextRoot>
						</webModule>
					</modules>
					<fileNameMapping>no-version</fileNameMapping>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>