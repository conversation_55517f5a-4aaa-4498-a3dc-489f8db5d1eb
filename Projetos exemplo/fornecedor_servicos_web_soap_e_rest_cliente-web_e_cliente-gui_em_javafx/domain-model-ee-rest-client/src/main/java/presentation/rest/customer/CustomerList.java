package presentation.rest.customer;

import java.util.LinkedList;
import java.util.List;

import presentation.rest.CollectionMeta;

public class CustomerList {

	private CollectionMeta meta;
    private List<CustomerGet> customers;

    public CustomerList() {
        this.customers = new LinkedList<>();
    }

    public void addUser(CustomerGet user) {
        customers.add(user);
    }

    public void setMeta(CollectionMeta meta) {
        this.meta = meta;
    }

    public int size() {
        return customers.size();
    }

    public List<CustomerGet> getCustomers() {
		return customers;
	}

	public void setCustomers(List<CustomerGet> customers) {
		this.customers = customers;
	}

	public CollectionMeta getMeta() {
		return meta;
	}

}


