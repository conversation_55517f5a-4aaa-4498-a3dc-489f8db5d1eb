package presentation.rest.api;

import java.util.List;

public class APIData {

	private List<Link> links;
	private ProductInfo productInfo;

    public APIData() {
    }

    public APIData (ProductInfo productInfo, List<Link> links) {
        this.productInfo = productInfo;
        this.links = links;
    }

    public List<Link> getLinks() {
		return links;
	}

	public void setLinks(List<Link> links) {
		this.links = links;
	}

	public ProductInfo getProductInfo() {
		return productInfo;
	}

	public void setProductInfo(ProductInfo productInfo) {
		this.productInfo = productInfo;
	}
 }
