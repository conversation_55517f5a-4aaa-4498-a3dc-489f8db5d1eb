package presentation.rest.customer;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

public class CustomerData implements Serializable {

    /**
     * Id for serialization 
     */
    private static final long serialVersionUID = -5679590895412845110L;

    private int vat;
	@NotNull private String denomination;
    private int phoneNumber;    
    private int discountType;
    
    public CustomerData() {
    }
    
    public int getVat() {
		return vat;
	}

	public void setVat(int vat) {
		this.vat = vat;
	}

	public int getDiscountType() {
		return discountType;
	}

	public void setDiscountType(int discountType) {
		this.discountType = discountType;
	}

	public void setDenomination(String denomination) {
		this.denomination = denomination;
	}

	public void setPhoneNumber(int phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

    public String getDenomination() {
		return denomination;
	}
    
    public int getPhoneNumber() {
        return phoneNumber;
    }

}
