package presentation.rest.customer;

import java.net.URI;
import java.net.URISyntaxException;

import javax.ejb.EJB;
import javax.ejb.Stateless;
import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.NotFoundException;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import javax.ws.rs.core.UriInfo;

import facade.dto.CustomerDTO;
import facade.exceptions.ApplicationException;
import facade.remoteservices.ICustomerServiceRemote;
import presentation.rest.CollectionMeta;

@Path(Customers.HREF)
@Produces({MediaType.APPLICATION_JSON})
@Consumes({MediaType.APPLICATION_JSON})
@Stateless
public class Customers {

	public static final String REL = "customers";
	public static final String HREF = "v1/customers";

	@EJB private ICustomerServiceRemote customerService;
	@Context private UriInfo uriInfo;    

	@POST
	public Response addCustomer(@Valid CustomerData customerData) throws URISyntaxException {
		try {
			CustomerDTO customer = customerService.addCustomer(customerData.getVat(), 
					customerData.getDenomination(), customerData.getPhoneNumber(), 
					customerData.getDiscountType());
			String href = uriInfo.getBaseUri().toString() + HREF + "/" + customer.id;
			return Response.created(new URI(href)).build();
		} catch (ApplicationException e) {
			return Response.status(Status.PRECONDITION_FAILED).build();
		}
	}

	@GET
	public Response getCustomers() {
		CustomerList res = new CustomerList();
		for (CustomerDTO customer : customerService.getCustomers()) {
			String href = uriInfo.getBaseUri().toString() + HREF + "/" + customer.id;
			res.addUser(new CustomerGet(href, customer));
		}
		res.setMeta(new CollectionMeta(res.size(), null, null, 1, res.size()));
		return Response.ok(res).build();
	}

	@GET
	@Path("{id}")
	public CustomerGet getCustomer(@PathParam("id") int id) {
		try {
			CustomerDTO c = customerService.getCustomer(id);
			String href = uriInfo.getBaseUri().toString() + HREF + "/" + c.id;
			return new CustomerGet(href, c);
		} catch (ApplicationException e) {
			throw new NotFoundException();
		}
	}

	@DELETE
	@Path("{id}")
	public Response deleteCustomer(@PathParam("id") int id) {
		try {
			customerService.deleteCustomer(id);
			return Response.ok().build();
		} catch (ApplicationException e) {
			throw new NotFoundException();
		}
	}

}
