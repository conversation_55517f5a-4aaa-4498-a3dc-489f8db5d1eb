package presentation.rest;

public class CollectionMeta {

	private int pageSize;
	private String nextPage;
	private String previousPage;
	private int currentPageNumber;
	private int totalCount;

	public CollectionMeta() {
	}

	public CollectionMeta(int pageSize, String nextPage, String previousPage,
			int currentPageNumer, int totalCount) {
		this.pageSize = pageSize;
		this.nextPage = nextPage;
		this.previousPage = previousPage;
		this.currentPageNumber = currentPageNumer;
		this.totalCount = totalCount;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public String getNextPage() {
		return nextPage;
	}

	public void setNextPage(String nextPage) {
		this.nextPage = nextPage;
	}

	public String getPreviousPage() {
		return previousPage;
	}

	public void setPreviousPage(String previousPage) {
		this.previousPage = previousPage;
	}

	public int getCurrentPageNumber() {
		return currentPageNumber;
	}

	public void setCurrentPageNumber(int currentPageNumber) {
		this.currentPageNumber = currentPageNumber;
	}

	public int getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}

}
