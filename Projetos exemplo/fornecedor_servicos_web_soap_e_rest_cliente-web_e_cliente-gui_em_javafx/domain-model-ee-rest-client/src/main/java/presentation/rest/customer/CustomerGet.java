package presentation.rest.customer;

import java.io.Serializable;

import facade.dto.CustomerDTO;

public class CustomerGet implements Serializable {

    private static final long serialVersionUID = 7243549697187713023L;

    private int id;
    private String href;
    private String designation;
    private int vatNumber;

	public CustomerGet() {
    }

	public CustomerGet(String href, CustomerDTO customer) {
        this.id = customer.id;
        this.href = href;
        this.designation = customer.designation;
        this.vatNumber = customer.vatNumber;
    }

    public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getHref() {
		return href;
	}

	public void setHref(String href) {
		this.href = href;
	}

	public String getDesignation() {
		return designation;
	}

	public void setDesignation(String designation) {
		this.designation = designation;
	}

	public int getVatNumber() {
		return vatNumber;
	}

	public void setVatNumber(int vatNumber) {
		this.vatNumber = vatNumber;
	}

 }
