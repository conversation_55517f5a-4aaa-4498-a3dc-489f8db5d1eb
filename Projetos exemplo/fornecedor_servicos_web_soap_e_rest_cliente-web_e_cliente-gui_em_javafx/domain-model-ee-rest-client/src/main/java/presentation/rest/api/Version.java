package presentation.rest.api;

public class Version {

	private int revision;
	private int build; 
	private int minor;
	private int major;

    public Version() {
    }

    public Version (int major, int minor, int revision, int build) {
        this.revision = revision;
        this.build = build;
        this.minor = minor;
        this.major = major;
    }

	public int getRevision() {
		return revision;
	}

	public void setRevision(int revision) {
		this.revision = revision;
	}

	public int getBuild() {
		return build;
	}

	public void setBuild(int build) {
		this.build = build;
	}

	public int getMinor() {
		return minor;
	}

	public void setMinor(int minor) {
		this.minor = minor;
	}

	public int getMajor() {
		return major;
	}

	public void setMajor(int major) {
		this.major = major;
	}
    
}
