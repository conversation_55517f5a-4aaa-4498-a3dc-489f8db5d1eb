package business;

import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import facade.exceptions.ApplicationException;
import facade.exceptions.VatInvalidException;

/**
 * Handles the add customer use case. This represents a very 
 * simplified use case with just one operation: addCustomer.
 * 
 * <AUTHOR>
 * <AUTHOR>
 * @version 1.2 (23/05/2018)
 *
 */
@Stateless
public class AddCustomerHandler {
	
	/**
	 * The customer's catalog
	 */
	@EJB
	private CustomerCatalog customerCatalog;
	
	/**
	 * The discount's catalog 
	 */
	@EJB
	private DiscountCatalog discountCatalog;
		
	/**
	 * Adds a new customer with a valid Number. It checks that there is no other 
	 * customer in the database with the same VAT.
	 * 
	 * @param vat The VAT of the customer
	 * @param denomination The customer's name
	 * @param phoneNumber The customer's phone 
	 * @param discountType The type of discount applicable to the customer
	 * @return The id of the created customer
	 * @throws ApplicationException When the VAT number is invalid (we check it according 
	 * to the Portuguese legislation) or there is already a customer with the same vat registered
	 * in the system.
	 */
	public int addCustomer (int vat, String denomination, 
			int phoneNumber, int discountType) throws ApplicationException {
		if(!Customer.isValidVAT(vat))
			throw new VatInvalidException(Integer.toString(vat));		
		Discount discount = discountCatalog.getDiscount(discountType);
		try {
			Customer c = customerCatalog.addCustomer(vat, denomination, phoneNumber, discount);
			return c.getId();
		} catch (Exception e) {
			throw new ApplicationException ("Error adding customer with VAT " + vat, e);
		}
	}	
	
	/**
	 * @return The list of discounts supported by the system.
	 */
	public List<Discount> getDiscounts() throws ApplicationException {
		return discountCatalog.getDiscounts();
	}

}
