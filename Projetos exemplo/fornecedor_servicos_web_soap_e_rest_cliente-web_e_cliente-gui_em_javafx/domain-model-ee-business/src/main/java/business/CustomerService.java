package business;

import java.util.LinkedList;
import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Stateless;
import javax.jws.WebService;

import facade.dto.CustomerDTO;
import facade.dto.DiscountDTO;
import facade.exceptions.ApplicationException;
import facade.remoteservices.ICustomerServiceRemote;

/**
 * Aggregates the services related with customers that are exposed in a remote facade.
 * 
 * <AUTHOR>
 * <AUTHOR>
 * @version 1.2 (23/05/2018)
 *
 */
@Stateless
@WebService
public class CustomerService implements ICustomerServiceRemote {
	
	/**
	 * The handlers of the different use cases
	 */
	@EJB
	private AddCustomerHandler addHandler;
	@EJB
	private RemoveCustomerHandler removeHandler;
	@EJB
	private GetCustomerHandler getHandler;
		
	@Override
	public CustomerDTO addCustomer (int vat, String denomination, 
			int phoneNumber, int discountType) throws ApplicationException {
			int id = addHandler.addCustomer(vat, denomination, phoneNumber, discountType);
			Customer c = getHandler.getCustomer(id);
			return new CustomerDTO(c.getVATNumber(), c.getDesignation(), c.getId());
	}	
	
	@Override
	public List<DiscountDTO> getDiscounts() throws ApplicationException {
		List<DiscountDTO> discounts = new LinkedList<>();
		for (Discount d : addHandler.getDiscounts())
			discounts.add(new DiscountDTO(d.getId(),d.getDescription()));
		return discounts;
	}

	public CustomerDTO getCustomer(int id) throws ApplicationException {
		Customer c = getHandler.getCustomer(id);
		return new CustomerDTO(c.getVATNumber(), c.getDesignation(), c.getId());
	}

	public List<CustomerDTO> getCustomers() {
		List<CustomerDTO> customers = new LinkedList<>();
		for (Customer c : getHandler.getCustomers())
			customers.add(new CustomerDTO(c.getVATNumber(), c.getDesignation(), c.getId()));
		return customers;
	}

	@Override
	public void deleteCustomer(int id) throws ApplicationException {
		removeHandler.deleteCustomer(id);
	}

}
