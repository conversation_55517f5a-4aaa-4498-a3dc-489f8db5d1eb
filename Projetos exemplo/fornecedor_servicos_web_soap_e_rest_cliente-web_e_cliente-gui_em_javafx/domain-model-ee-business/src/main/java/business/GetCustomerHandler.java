package business;

import javax.ejb.EJB;
import javax.ejb.Stateless;

import facade.exceptions.ApplicationException;

/**
 * Handles the two different get customer use cases. 
 * 
 * <AUTHOR>
 * <AUTHOR>
 * @version 1.2 (23/05/2018)
 *
 */
@Stateless
public class GetCustomerHandler {
	
	/**
	 * The customer's catalog
	 */
	@EJB
	private CustomerCatalog customerCatalog;
	
	public Customer getCustomer(int id) throws ApplicationException {
		return  customerCatalog.getCustomerById(id);
	}

	public Iterable<Customer> getCustomers() {
		return customerCatalog.getCustomers();
	}

}
