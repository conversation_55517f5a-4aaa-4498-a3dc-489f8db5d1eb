package facade.remoteservices;

import javax.ejb.Remote;

import business.Customer;
import business.Discount;
import facade.dto.CustomerDTO;
import facade.dto.DiscountDTO;
import facade.exceptions.ApplicationException;

@Remote
public interface ICustomerServiceRemote {

	/**
	 * Adds a new customer with a valid Number. It checks that there is no other 
	 * customer in the database with the same VAT.
	 * 
	 * @param vat The VAT of the customer
	 * @param denomination The customer's name
	 * @param phoneNumber The customer's phone 
	 * @param discountType The type of discount applicable to the customer
	 * @return The data concerning the created customer
	 * @throws ApplicationException When the VAT number is invalid (we check it according 
	 * to the Portuguese legislation) or there is already a customer with the same vat registered
	 * in the system.
	 */
	public CustomerDTO addCustomer (int vat, String denomination, 
			int phoneNumber, int discountType) 
			throws ApplicationException;
	
	/**
	 * @return The type of discounts supported by the system.
	 */
	public Iterable<DiscountDTO> getDiscounts() throws ApplicationException;
	

	/**
	 * Removes a customer with a given id
	 * 
	 * @param vat The vat of the customer
	 * @return The data concerning the customer with the given id
	 * @throws ApplicationException When the VAT number is invalid 
	 * or there is no customer with that vat registered in the system.
	 */
	public void deleteCustomer(int id) throws ApplicationException;
	
	/**
	 * Gets the data of a customer with a given id
	 */ 
	public CustomerDTO getCustomer(int id) throws ApplicationException;

	/**
	 * Gets the data of all customers registered in the system
	 */ 
	public Iterable<CustomerDTO> getCustomers();

}
