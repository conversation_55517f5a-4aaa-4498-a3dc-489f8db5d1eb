<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.0"
	xmlns="http://java.sun.com/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd">

	<persistence-unit name="domain-model-ee-business"
		transaction-type="JTA">
		<!-- The datasource is deployed as <EAR>/META-INF/sri-srv-ds.xml, you can 
			find it in the source at ear/src/main/resources/META-INF/domain-model-ee-ds.xml -->
		<jta-data-source>java:/jdbc/domain_model_ee_ds</jta-data-source>
		<exclude-unlisted-classes>false</exclude-unlisted-classes>
		<shared-cache-mode>NONE</shared-cache-mode>
		<properties>
			<property name="javax.persistence.schema-generation.database.action"
				value="drop-and-create" />
			<property name="javax.persistence.schema-generation.create-source"
				value="metadata" />
			<property name="javax.persistence.schema-generation.drop-source"
				value="metadata" />
			<property name="javax.persistence.sql-load-script-source"
				value="META-INF/load-script.sql" />
		</properties>
	</persistence-unit>
</persistence>
