<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>pt.ulisboa.ciencias.di</groupId>
		<artifactId>domain-model-ee</artifactId>
		<version>1.0</version>
	</parent>

	<artifactId>domain-model-ee-business</artifactId>
	<packaging>ejb</packaging>

	<dependencies>

		<!-- Declare the APIs we depend on and need for compilation. All of them 
			are provided by JBoss WildFly -->

		<!-- Import the EJB API, we use provided scope as the API is included in 
			JBoss WildFly -->
		<dependency>
			<groupId>org.jboss.spec.javax.ejb</groupId>
			<artifactId>jboss-ejb-api_3.2_spec</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- Import the JPA API, we use provided scope as the API is included in 
			JBoss WildFly -->
		<dependency>
			<groupId>org.hibernate.javax.persistence</groupId>
			<artifactId>hibernate-jpa-2.1-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- JSR-303 (Bean Validation) Implementation -->
		<!-- Provides portable constraints such as @Email -->
		<!-- Hibernate Validator is shipped in JBoss WildFly -->
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- Import the JTA API -->
		<dependency>
			<groupId>org.jboss.spec.javax.transaction</groupId>
			<artifactId>jboss-transaction-api_1.2_spec</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.38</version>
		</dependency>

	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<artifactId>maven-ejb-plugin</artifactId>
				<version>${version.ejb.plugin}</version>
				<configuration>
					<!-- Tell Maven we are using EJB 3.1 -->
					<ejbVersion>3.1</ejbVersion>
					<generateClient>true</generateClient>
					<clientIncludes>
						<clientInclude>facade/**</clientInclude>
					</clientIncludes>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>


