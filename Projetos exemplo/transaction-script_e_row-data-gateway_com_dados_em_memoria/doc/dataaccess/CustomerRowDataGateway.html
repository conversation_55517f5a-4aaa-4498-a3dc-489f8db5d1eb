<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_77) on Thu Mar 02 07:58:07 WET 2017 -->
<title>CustomerRowDataGateway</title>
<meta name="date" content="2017-03-02">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CustomerRowDataGateway";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CustomerRowDataGateway.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../dataaccess/ConfigurationRowDataGateway.html" title="enum in dataaccess"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../dataaccess/PersistenceException.html" title="class in dataaccess"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/CustomerRowDataGateway.html" target="_top">Frames</a></li>
<li><a href="CustomerRowDataGateway.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">dataaccess</div>
<h2 title="Class CustomerRowDataGateway" class="title">Class CustomerRowDataGateway</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>dataaccess.CustomerRowDataGateway</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CustomerRowDataGateway</span>
extends java.lang.Object</pre>
<div class="block">An in-memory representation of a row gateway for a customer.</div>
<dl>
<dt><span class="simpleTagLabel">Version:</span></dt>
<dd>1.1 (4/10/2014)</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>fmartins</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#CustomerRowDataGateway-int-java.lang.String-int-datatypes.DiscountType-">CustomerRowDataGateway</a></span>(int&nbsp;vat,
                      java.lang.String&nbsp;designation,
                      int&nbsp;phoneNumber,
                      datatypes.DiscountType&nbsp;discountType)</code>
<div class="block">Creates a new customer given its VAT number, its designation, phone contact, and discount type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getCustomerById-int-">getCustomerById</a></span>(int&nbsp;id)</code>
<div class="block">Fetches a customer given its internal id number and returns a CustomerRowGateway 
 object with the data retrieved from the repository.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.util.Optional&lt;<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getCustomerByVATNumber-int-">getCustomerByVATNumber</a></span>(int&nbsp;vat)</code>
<div class="block">Fetches a customer given its VAT number and returns a CustomerRowGateway 
 object with the data retrieved from the repository.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getCustomerId--">getCustomerId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getDesignation--">getDesignation</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>datatypes.DiscountType</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getDiscountType--">getDiscountType</a></span>()</code>
<div class="block">Notice that the setter and the getter converts the integer representing
 the discount id to an enumerated value (of type DiscountType) that is 
 then used in the application's domain logic.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getPhoneNumber--">getPhoneNumber</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getVAT--">getVAT</a></span>()</code>
<div class="block">Comment: There is a business logic decision not to allow changes to the VAT number
 of a customer.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#insert--">insert</a></span>()</code>
<div class="block">Stores the information in the repository.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#setDesignation-java.lang.String-">setDesignation</a></span>(java.lang.String&nbsp;designation)</code>
<div class="block">Updates the customer designation.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#setDiscountType-datatypes.DiscountType-">setDiscountType</a></span>(datatypes.DiscountType&nbsp;discountType)</code>
<div class="block">Updates the discount id of the customer</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#setPhoneNumber-int-">setPhoneNumber</a></span>(int&nbsp;phoneNumber)</code>
<div class="block">Updates the phone number</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CustomerRowDataGateway-int-java.lang.String-int-datatypes.DiscountType-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CustomerRowDataGateway</h4>
<pre>public&nbsp;CustomerRowDataGateway(int&nbsp;vat,
                              java.lang.String&nbsp;designation,
                              int&nbsp;phoneNumber,
                              datatypes.DiscountType&nbsp;discountType)</pre>
<div class="block">Creates a new customer given its VAT number, its designation, phone contact, and discount type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>vat</code> - The customer's VAT number</dd>
<dd><code>designation</code> - The customer's designation</dd>
<dd><code>phoneNumber</code> - The customer's phone number</dd>
<dd><code>discountType</code> - The customer's discount type</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCustomerId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCustomerId</h4>
<pre>public&nbsp;int&nbsp;getCustomerId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The id of the customer

 There is no need for a setter, since this is an internal 
 database number (a sequential, primary key) that is not 
 available at the application level for change.</dd>
</dl>
</li>
</ul>
<a name="getVAT--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVAT</h4>
<pre>public&nbsp;int&nbsp;getVAT()</pre>
<div class="block">Comment: There is a business logic decision not to allow changes to the VAT number
 of a customer. We can reflect this decision in the row data gateway by not providing 
 the setVAT method. However, in a more mechanical approach where every field has a 
 getter and a setter, it is perfectly plausible to provide a setter method as well.

 Whenever there is repeated code (specially when using transaction script), 
 these decisions can migrate to the row gateway and it starts being converted 
 into an active record style. Nevertheless, use it with care and bare in mind that 
 business rules are enforced by the domain logic. When the "migration" to active record 
 starts, please move the class to the business logic package.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The customer's VAT number.</dd>
</dl>
</li>
</ul>
<a name="getDesignation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDesignation</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDesignation()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The customer's designation.</dd>
</dl>
</li>
</ul>
<a name="setDesignation-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDesignation</h4>
<pre>public&nbsp;void&nbsp;setDesignation(java.lang.String&nbsp;designation)</pre>
<div class="block">Updates the customer designation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>designation</code> - The new designation to change to.</dd>
</dl>
</li>
</ul>
<a name="getPhoneNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhoneNumber</h4>
<pre>public&nbsp;int&nbsp;getPhoneNumber()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The customer's phone number</dd>
</dl>
</li>
</ul>
<a name="setPhoneNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhoneNumber</h4>
<pre>public&nbsp;void&nbsp;setPhoneNumber(int&nbsp;phoneNumber)</pre>
<div class="block">Updates the phone number</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>phoneNumber</code> - The new phone number</dd>
</dl>
</li>
</ul>
<a name="getDiscountType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDiscountType</h4>
<pre>public&nbsp;datatypes.DiscountType&nbsp;getDiscountType()</pre>
<div class="block">Notice that the setter and the getter converts the integer representing
 the discount id to an enumerated value (of type DiscountType) that is 
 then used in the application's domain logic. The internal id (primary
 key) must not be used directly for implementing business rules.
 
 Observation: should the persistence layer refers to a DiscountType class that
 is at the application layer? On the other hand, shall the application level,
 in particular the DiscountType class, take care of value conversion for 
 serialization purposes?</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The discount id associated with the customer</dd>
</dl>
</li>
</ul>
<a name="setDiscountType-datatypes.DiscountType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDiscountType</h4>
<pre>public&nbsp;void&nbsp;setDiscountType(datatypes.DiscountType&nbsp;discountType)</pre>
<div class="block">Updates the discount id of the customer</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>discountType</code> - The new discount type</dd>
</dl>
</li>
</ul>
<a name="insert--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insert</h4>
<pre>public&nbsp;void&nbsp;insert()</pre>
<div class="block">Stores the information in the repository.
 In the present case it stores in both maps, since we need to access the
 data using two different keys.</div>
</li>
</ul>
<a name="getCustomerByVATNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCustomerByVATNumber</h4>
<pre>public static&nbsp;java.util.Optional&lt;<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a>&gt;&nbsp;getCustomerByVATNumber(int&nbsp;vat)</pre>
<div class="block">Fetches a customer given its VAT number and returns a CustomerRowGateway 
 object with the data retrieved from the repository. Notice that the method
 returns an Optional value (https://docs.oracle.com/javase/8/docs/api/java/util/Optional.html).
 It means that the method can either return a customer or an empty optional 
 object, signaling the clients of this method that no customer was found with
 the specified VAT number.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>vat</code> - The VAT number of the customer to fetch from the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The CustomerRowGateway corresponding to the customer with the vat number, in case it exists;
 otherwise it returns an empty optional value.</dd>
</dl>
</li>
</ul>
<a name="getCustomerById-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCustomerById</h4>
<pre>public static&nbsp;<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a>&nbsp;getCustomerById(int&nbsp;id)
                                              throws <a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess">RecordNotFoundException</a></pre>
<div class="block">Fetches a customer given its internal id number and returns a CustomerRowGateway 
 object with the data retrieved from the repository. In case the customer
 is not found, a RecordNotFoundException is thrown.
 
 Note: Here I decided to throw an exception instead of an Optional value like in 
 method getCustomerByVATNumber. Why? Because a customer id should never be used for
 other purposes than for internally SQL relations, unlike a VAT number that can 
 be fetched by a client method, for instance, to check if a customer is in the
 repository. This operation is completely acceptable, whereas accessing a customer by 
 its internal id and not being able to find it is much like accessing a null reference
 in an OO program. Hence the throwing of a RecordNotFoundException.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - The Id of the customer to fetch from the repository.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The CustomerRowGateway corresponding to the customer with the id number.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess">RecordNotFoundException</a></code> - When the customer with the given id number is not found.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CustomerRowDataGateway.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../dataaccess/ConfigurationRowDataGateway.html" title="enum in dataaccess"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../dataaccess/PersistenceException.html" title="class in dataaccess"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/CustomerRowDataGateway.html" target="_top">Frames</a></li>
<li><a href="CustomerRowDataGateway.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
