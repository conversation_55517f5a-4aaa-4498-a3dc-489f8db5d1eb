<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_77) on Thu Mar 02 07:58:07 WET 2017 -->
<title>SaleRowDataGateway</title>
<meta name="date" content="2017-03-02">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SaleRowDataGateway";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":9,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SaleRowDataGateway.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/SaleRowDataGateway.html" target="_top">Frames</a></li>
<li><a href="SaleRowDataGateway.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">dataaccess</div>
<h2 title="Class SaleRowDataGateway" class="title">Class SaleRowDataGateway</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>dataaccess.SaleRowDataGateway</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SaleRowDataGateway</span>
extends java.lang.Object</pre>
<div class="block">An in-memory representation of a row gateway for a sale row.</div>
<dl>
<dt><span class="simpleTagLabel">Version:</span></dt>
<dd>1.1 (4/10/2014)</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>fmartins</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#SaleRowDataGateway-int-java.util.Date-">SaleRowDataGateway</a></span>(int&nbsp;clientId,
                  java.util.Date&nbsp;date)</code>
<div class="block">Creates a new sale given the client Id and the date it occurs.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getClientId--">getClientId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.util.Date</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getDate--">getDate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getDiscount--">getDiscount</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getId--">getId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.util.Optional&lt;<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getSaleById-int-">getSaleById</a></span>(int&nbsp;id)</code>
<div class="block">Fetches a sale with a given saleId and returns a sale row gateway object, 
 in case it exists (Optional).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>datatypes.SaleStatus</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getStatus--">getStatus</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getTotal--">getTotal</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#insert--">insert</a></span>()</code>
<div class="block">Stores the information in the repository</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#setDiscount-double-">setDiscount</a></span>(double&nbsp;discount)</code>
<div class="block">Updates the sales total discount</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#setStatus-datatypes.SaleStatus-">setStatus</a></span>(datatypes.SaleStatus&nbsp;status)</code>
<div class="block">Updates the sale's status
 Notice that the setter and the getter convert the string to an 
 enumerated value (of type SaleStatus) that is then used in the
 application's domain logic.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#setTotal-double-">setTotal</a></span>(double&nbsp;total)</code>
<div class="block">Updates the sales total</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SaleRowDataGateway-int-java.util.Date-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SaleRowDataGateway</h4>
<pre>public&nbsp;SaleRowDataGateway(int&nbsp;clientId,
                          java.util.Date&nbsp;date)</pre>
<div class="block">Creates a new sale given the client Id and the date it occurs.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>clientId</code> - The client Id the sale belongs to</dd>
<dd><code>date</code> - The date the sale took place</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDate</h4>
<pre>public&nbsp;java.util.Date&nbsp;getDate()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sale's date</dd>
</dl>
</li>
</ul>
<a name="getTotal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotal</h4>
<pre>public&nbsp;double&nbsp;getTotal()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sale's total</dd>
</dl>
</li>
</ul>
<a name="setTotal-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTotal</h4>
<pre>public&nbsp;void&nbsp;setTotal(double&nbsp;total)</pre>
<div class="block">Updates the sales total</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>total</code> - The new total</dd>
</dl>
</li>
</ul>
<a name="getDiscount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDiscount</h4>
<pre>public&nbsp;double&nbsp;getDiscount()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sale's discount</dd>
</dl>
</li>
</ul>
<a name="setDiscount-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDiscount</h4>
<pre>public&nbsp;void&nbsp;setDiscount(double&nbsp;discount)</pre>
<div class="block">Updates the sales total discount</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>discount</code> - The new discount total amount</dd>
</dl>
</li>
</ul>
<a name="getStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatus</h4>
<pre>public&nbsp;datatypes.SaleStatus&nbsp;getStatus()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sale's status.</dd>
</dl>
</li>
</ul>
<a name="setStatus-datatypes.SaleStatus-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatus</h4>
<pre>public&nbsp;void&nbsp;setStatus(datatypes.SaleStatus&nbsp;status)</pre>
<div class="block">Updates the sale's status
 Notice that the setter and the getter convert the string to an 
 enumerated value (of type SaleStatus) that is then used in the
 application's domain logic.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - The new sale status</dd>
</dl>
</li>
</ul>
<a name="getClientId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClientId</h4>
<pre>public&nbsp;int&nbsp;getClientId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The client's Id associated with this sale</dd>
</dl>
</li>
</ul>
<a name="getId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre>public&nbsp;int&nbsp;getId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sale's Id</dd>
</dl>
</li>
</ul>
<a name="insert--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insert</h4>
<pre>public&nbsp;void&nbsp;insert()</pre>
<div class="block">Stores the information in the repository</div>
</li>
</ul>
<a name="getSaleById-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSaleById</h4>
<pre>public static&nbsp;java.util.Optional&lt;<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a>&gt;&nbsp;getSaleById(int&nbsp;id)</pre>
<div class="block">Fetches a sale with a given saleId and returns a sale row gateway object, 
 in case it exists (Optional).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - The id of the sale to find in the repository</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A sale row gateway object with the sale whose id matches saleId,
 in case it exists; otherwise it returns an empty optional value.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SaleRowDataGateway.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/SaleRowDataGateway.html" target="_top">Frames</a></li>
<li><a href="SaleRowDataGateway.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
