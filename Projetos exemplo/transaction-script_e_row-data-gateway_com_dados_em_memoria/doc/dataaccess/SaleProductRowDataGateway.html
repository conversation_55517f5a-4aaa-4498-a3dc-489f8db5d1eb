<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_77) on Thu Mar 02 07:58:07 WET 2017 -->
<title>SaleProductRowDataGateway</title>
<meta name="date" content="2017-03-02">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SaleProductRowDataGateway";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":9,"i4":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SaleProductRowDataGateway.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/SaleProductRowDataGateway.html" target="_top">Frames</a></li>
<li><a href="SaleProductRowDataGateway.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">dataaccess</div>
<h2 title="Class SaleProductRowDataGateway" class="title">Class SaleProductRowDataGateway</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>dataaccess.SaleProductRowDataGateway</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SaleProductRowDataGateway</span>
extends java.lang.Object</pre>
<div class="block">An in-memory representation of a row gateway for the products composing a sale.</div>
<dl>
<dt><span class="simpleTagLabel">Version:</span></dt>
<dd>1.1 (4/10/2014)</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>fmartins</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#SaleProductRowDataGateway-int-int-double-">SaleProductRowDataGateway</a></span>(int&nbsp;saleId,
                         int&nbsp;productId,
                         double&nbsp;qty)</code>
<div class="block">Creates an association between a saleId and a productId.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#getProductId--">getProductId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#getQty--">getQty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#getSaleId--">getSaleId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.Iterable&lt;<a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess">SaleProductRowDataGateway</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#getSaleProducts-int-">getSaleProducts</a></span>(int&nbsp;saleId)</code>
<div class="block">Obtains the products of a given saleId.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#insert--">insert</a></span>()</code>
<div class="block">Inserts the record in the products sale</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SaleProductRowDataGateway-int-int-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SaleProductRowDataGateway</h4>
<pre>public&nbsp;SaleProductRowDataGateway(int&nbsp;saleId,
                                 int&nbsp;productId,
                                 double&nbsp;qty)</pre>
<div class="block">Creates an association between a saleId and a productId. The qty is the 
 quantity of items in the sale.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>saleId</code> - The sale to associate a product to.</dd>
<dd><code>productId</code> - The product to be associated with the sale.</dd>
<dd><code>qty</code> - The number of products sold.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSaleId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSaleId</h4>
<pre>public&nbsp;int&nbsp;getSaleId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sale id of the product sale</dd>
</dl>
</li>
</ul>
<a name="getProductId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProductId</h4>
<pre>public&nbsp;int&nbsp;getProductId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The product id of the product sale</dd>
</dl>
</li>
</ul>
<a name="getQty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQty</h4>
<pre>public&nbsp;double&nbsp;getQty()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The quantity of the product sale</dd>
</dl>
</li>
</ul>
<a name="insert--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insert</h4>
<pre>public&nbsp;void&nbsp;insert()</pre>
<div class="block">Inserts the record in the products sale</div>
</li>
</ul>
<a name="getSaleProducts-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSaleProducts</h4>
<pre>public static&nbsp;java.lang.Iterable&lt;<a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess">SaleProductRowDataGateway</a>&gt;&nbsp;getSaleProducts(int&nbsp;saleId)
                                                                     throws <a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess">RecordNotFoundException</a></pre>
<div class="block">Obtains the products of a given saleId. Raises RecordNotFoundException in 
 case the sale is not found.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>saleId</code> - The sale id to obtain the products from.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>An iterable with the products of the saleId.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess">RecordNotFoundException</a></code> - When the sale with saleId number is not found.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SaleProductRowDataGateway.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/SaleProductRowDataGateway.html" target="_top">Frames</a></li>
<li><a href="SaleProductRowDataGateway.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
