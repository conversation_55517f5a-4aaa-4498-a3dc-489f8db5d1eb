<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_77) on Thu Mar 02 07:58:07 WET 2017 -->
<title>ProductRowDataGateway</title>
<meta name="date" content="2017-03-02">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProductRowDataGateway";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":9,"i4":9,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProductRowDataGateway.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../dataaccess/PersistenceException.html" title="class in dataaccess"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/ProductRowDataGateway.html" target="_top">Frames</a></li>
<li><a href="ProductRowDataGateway.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">dataaccess</div>
<h2 title="Class ProductRowDataGateway" class="title">Class ProductRowDataGateway</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>dataaccess.ProductRowDataGateway</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ProductRowDataGateway</span>
extends java.lang.Object</pre>
<div class="block">An in-memory representation of a row gateway for a product.</div>
<dl>
<dt><span class="simpleTagLabel">Version:</span></dt>
<dd>1.1 (4/10/2014)</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>fmartins</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#ProductRowDataGateway-int-java.lang.String-double-double-boolean-int-">ProductRowDataGateway</a></span>(int&nbsp;prodCod,
                     java.lang.String&nbsp;description,
                     double&nbsp;faceValue,
                     double&nbsp;qty,
                     boolean&nbsp;eligibleForDiscount,
                     int&nbsp;unitId)</code>
<div class="block">Creates a in-memory representation of a product given its
 product code, description, face value, quantity in stock,
 discount eligibility, and the units in which the quantity is
 expressed.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getDescription--">getDescription</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getFaceValue--">getFaceValue</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getProdCod--">getProdCod</a></span>()</code>
<div class="block">Comment: there is a business rule to not allow product code changes.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.util.Optional&lt;<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getProductByCode-int-">getProductByCode</a></span>(int&nbsp;prodCod)</code>
<div class="block">Finds a product with prodCod and returns its ProductRowGateway, in case
 it exists (Optional).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getProductById-int-">getProductById</a></span>(int&nbsp;prodId)</code>
<div class="block">Finds a product given its id and returns its ProductRowGateway.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getProductId--">getProductId</a></span>()</code>
<div class="block">Comment: the product id is an internal (persistence) concept only used
 to link entities and must not have any business meaning.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getQty--">getQty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getUnitId--">getUnitId</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#isEligibleForDiscount--">isEligibleForDiscount</a></span>()</code>
<div class="block">Notice that the setter and the getter convert the string to a 
 boolean value that is then used in the application's domain logic.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setDescription-java.lang.String-">setDescription</a></span>(java.lang.String&nbsp;description)</code>
<div class="block">Updates the product's description.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setEligibleForDiscount-boolean-">setEligibleForDiscount</a></span>(boolean&nbsp;eligibleForDiscount)</code>
<div class="block">Updates the eligibility condition of a product.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setFaceValue-double-">setFaceValue</a></span>(double&nbsp;faceValue)</code>
<div class="block">Updates the product's face value</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setQty-double-">setQty</a></span>(double&nbsp;qty)</code>
<div class="block">Updates the product's stock quantity</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setUnitId-int-">setUnitId</a></span>(int&nbsp;unitId)</code>
<div class="block">Updates the units id of the product.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#updateStockValue--">updateStockValue</a></span>()</code>
<div class="block">Updates the stock value</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ProductRowDataGateway-int-java.lang.String-double-double-boolean-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ProductRowDataGateway</h4>
<pre>public&nbsp;ProductRowDataGateway(int&nbsp;prodCod,
                             java.lang.String&nbsp;description,
                             double&nbsp;faceValue,
                             double&nbsp;qty,
                             boolean&nbsp;eligibleForDiscount,
                             int&nbsp;unitId)</pre>
<div class="block">Creates a in-memory representation of a product given its
 product code, description, face value, quantity in stock,
 discount eligibility, and the units in which the quantity is
 expressed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prodCod</code> - The product's code</dd>
<dd><code>description</code> - The description of the product</dd>
<dd><code>faceValue</code> - The value the product is sold</dd>
<dd><code>qty</code> - The number of units available in stock</dd>
<dd><code>eligibleForDiscount</code> - If the product is eligible for discount</dd>
<dd><code>unitId</code> - The units in which the quantity of the product is expressed in</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProductId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProductId</h4>
<pre>public&nbsp;int&nbsp;getProductId()</pre>
<div class="block">Comment: the product id is an internal (persistence) concept only used
 to link entities and must not have any business meaning. Also, there
 is no setProductId, since the id does not change through the execution
 of the program.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The product's id</dd>
</dl>
</li>
</ul>
<a name="getProdCod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProdCod</h4>
<pre>public&nbsp;int&nbsp;getProdCod()</pre>
<div class="block">Comment: there is a business rule to not allow product code changes.
 That is why there is no method for updating the product code.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The code of the product.</dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDescription()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The product's description.</dd>
</dl>
</li>
</ul>
<a name="setDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescription</h4>
<pre>public&nbsp;void&nbsp;setDescription(java.lang.String&nbsp;description)</pre>
<div class="block">Updates the product's description.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>description</code> - The new description for the product</dd>
</dl>
</li>
</ul>
<a name="getFaceValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFaceValue</h4>
<pre>public&nbsp;double&nbsp;getFaceValue()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The product's face value</dd>
</dl>
</li>
</ul>
<a name="setFaceValue-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFaceValue</h4>
<pre>public&nbsp;void&nbsp;setFaceValue(double&nbsp;faceValue)</pre>
<div class="block">Updates the product's face value</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>faceValue</code> - The new face value for the product</dd>
</dl>
</li>
</ul>
<a name="getQty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQty</h4>
<pre>public&nbsp;double&nbsp;getQty()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The product's quantity</dd>
</dl>
</li>
</ul>
<a name="setQty-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQty</h4>
<pre>public&nbsp;void&nbsp;setQty(double&nbsp;qty)</pre>
<div class="block">Updates the product's stock quantity</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>qty</code> - The new stock quantity</dd>
</dl>
</li>
</ul>
<a name="isEligibleForDiscount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEligibleForDiscount</h4>
<pre>public&nbsp;boolean&nbsp;isEligibleForDiscount()</pre>
<div class="block">Notice that the setter and the getter convert the string to a 
 boolean value that is then used in the application's domain logic.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>whether the product is eligible for discount</dd>
</dl>
</li>
</ul>
<a name="setEligibleForDiscount-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEligibleForDiscount</h4>
<pre>public&nbsp;void&nbsp;setEligibleForDiscount(boolean&nbsp;eligibleForDiscount)</pre>
<div class="block">Updates the eligibility condition of a product.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>eligibleForDiscount</code> - The new eligibility condition for the product.</dd>
</dl>
</li>
</ul>
<a name="getUnitId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnitId</h4>
<pre>public&nbsp;int&nbsp;getUnitId()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The units id of the product</dd>
</dl>
</li>
</ul>
<a name="setUnitId-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnitId</h4>
<pre>public&nbsp;void&nbsp;setUnitId(int&nbsp;unitId)</pre>
<div class="block">Updates the units id of the product.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>unitId</code> - The new units id of the product.</dd>
</dl>
</li>
</ul>
<a name="getProductByCode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProductByCode</h4>
<pre>public static&nbsp;java.util.Optional&lt;<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a>&gt;&nbsp;getProductByCode(int&nbsp;prodCod)</pre>
<div class="block">Finds a product with prodCod and returns its ProductRowGateway, in case
 it exists (Optional).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prodCod</code> - The code of the product to find</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ProductRowGateway of the product with a given prodCod, in case it exists;
 otherwise it returns an empty optional value.</dd>
</dl>
</li>
</ul>
<a name="getProductById-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProductById</h4>
<pre>public static&nbsp;<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a>&nbsp;getProductById(int&nbsp;prodId)
                                            throws <a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess">RecordNotFoundException</a></pre>
<div class="block">Finds a product given its id and returns its ProductRowGateway.
 Raises a RecordNotFoundException in case the id is no found.
 See comment in CustomerRowDataGateway.getCustomerById method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>prodId</code> - The id of the product to find</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ProductRowGateway of the product with a given id</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess">RecordNotFoundException</a></code> - When the product with the given is id not found.</dd>
</dl>
</li>
</ul>
<a name="updateStockValue--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>updateStockValue</h4>
<pre>public&nbsp;void&nbsp;updateStockValue()</pre>
<div class="block">Updates the stock value</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProductRowDataGateway.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../dataaccess/PersistenceException.html" title="class in dataaccess"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../dataaccess/RecordNotFoundException.html" title="class in dataaccess"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/ProductRowDataGateway.html" target="_top">Frames</a></li>
<li><a href="ProductRowDataGateway.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
