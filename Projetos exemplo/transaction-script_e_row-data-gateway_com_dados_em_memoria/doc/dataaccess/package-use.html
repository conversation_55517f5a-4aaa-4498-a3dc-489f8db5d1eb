<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_77) on Thu Mar 02 07:58:07 WET 2017 -->
<title>Uses of Package dataaccess</title>
<meta name="date" content="2017-03-02">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package dataaccess";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package dataaccess" class="title">Uses of Package<br>dataaccess</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList"><a name="dataaccess">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../dataaccess/package-summary.html">dataaccess</a> used by <a href="../dataaccess/package-summary.html">dataaccess</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../dataaccess/class-use/ConfigurationRowDataGateway.html#dataaccess">ConfigurationRowDataGateway</a>
<div class="block">An in-memory representation of a row gateway for a configuration row.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../dataaccess/class-use/CustomerRowDataGateway.html#dataaccess">CustomerRowDataGateway</a>
<div class="block">An in-memory representation of a row gateway for a customer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../dataaccess/class-use/PersistenceException.html#dataaccess">PersistenceException</a>
<div class="block">The top level exception for the persistence layer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../dataaccess/class-use/ProductRowDataGateway.html#dataaccess">ProductRowDataGateway</a>
<div class="block">An in-memory representation of a row gateway for a product.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../dataaccess/class-use/RecordNotFoundException.html#dataaccess">RecordNotFoundException</a>
<div class="block">Record not found exception.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../dataaccess/class-use/SaleProductRowDataGateway.html#dataaccess">SaleProductRowDataGateway</a>
<div class="block">An in-memory representation of a row gateway for the products composing a sale.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../dataaccess/class-use/SaleRowDataGateway.html#dataaccess">SaleRowDataGateway</a>
<div class="block">An in-memory representation of a row gateway for a sale row.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-files/index-1.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../index.html?dataaccess/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
