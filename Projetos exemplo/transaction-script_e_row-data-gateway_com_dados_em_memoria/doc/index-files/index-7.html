<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_77) on Thu Mar 02 07:58:08 WET 2017 -->
<title>S-Index</title>
<meta name="date" content="2017-03-02">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="S-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="../dataaccess/package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">Prev Letter</a></li>
<li><a href="index-8.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">Frames</a></li>
<li><a href="index-7.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">C</a>&nbsp;<a href="index-2.html">D</a>&nbsp;<a href="index-3.html">G</a>&nbsp;<a href="index-4.html">I</a>&nbsp;<a href="index-5.html">P</a>&nbsp;<a href="index-6.html">R</a>&nbsp;<a href="index-7.html">S</a>&nbsp;<a href="index-8.html">U</a>&nbsp;<a href="index-9.html">V</a>&nbsp;<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess"><span class="typeNameLink">SaleProductRowDataGateway</span></a> - Class in <a href="../dataaccess/package-summary.html">dataaccess</a></dt>
<dd>
<div class="block">An in-memory representation of a row gateway for the products composing a sale.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#SaleProductRowDataGateway-int-int-double-">SaleProductRowDataGateway(int, int, double)</a></span> - Constructor for class dataaccess.<a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess">SaleProductRowDataGateway</a></dt>
<dd>
<div class="block">Creates an association between a saleId and a productId.</div>
</dd>
<dt><a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess"><span class="typeNameLink">SaleRowDataGateway</span></a> - Class in <a href="../dataaccess/package-summary.html">dataaccess</a></dt>
<dd>
<div class="block">An in-memory representation of a row gateway for a sale row.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#SaleRowDataGateway-int-java.util.Date-">SaleRowDataGateway(int, Date)</a></span> - Constructor for class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>
<div class="block">Creates a new sale given the client Id and the date it occurs.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setDescription-java.lang.String-">setDescription(String)</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Updates the product's description.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#setDesignation-java.lang.String-">setDesignation(String)</a></span> - Method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>
<div class="block">Updates the customer designation.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#setDiscount-double-">setDiscount(double)</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>
<div class="block">Updates the sales total discount</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#setDiscountType-datatypes.DiscountType-">setDiscountType(DiscountType)</a></span> - Method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>
<div class="block">Updates the discount id of the customer</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setEligibleForDiscount-boolean-">setEligibleForDiscount(boolean)</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Updates the eligibility condition of a product.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setFaceValue-double-">setFaceValue(double)</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Updates the product's face value</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#setPhoneNumber-int-">setPhoneNumber(int)</a></span> - Method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>
<div class="block">Updates the phone number</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setQty-double-">setQty(double)</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Updates the product's stock quantity</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#setStatus-datatypes.SaleStatus-">setStatus(SaleStatus)</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>
<div class="block">Updates the sale's status
 Notice that the setter and the getter convert the string to an 
 enumerated value (of type SaleStatus) that is then used in the
 application's domain logic.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#setTotal-double-">setTotal(double)</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>
<div class="block">Updates the sales total</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#setUnitId-int-">setUnitId(int)</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Updates the units id of the product.</div>
</dd>
</dl>
<a href="index-1.html">C</a>&nbsp;<a href="index-2.html">D</a>&nbsp;<a href="index-3.html">G</a>&nbsp;<a href="index-4.html">I</a>&nbsp;<a href="index-5.html">P</a>&nbsp;<a href="index-6.html">R</a>&nbsp;<a href="index-7.html">S</a>&nbsp;<a href="index-8.html">U</a>&nbsp;<a href="index-9.html">V</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="../dataaccess/package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">Prev Letter</a></li>
<li><a href="index-8.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">Frames</a></li>
<li><a href="index-7.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
