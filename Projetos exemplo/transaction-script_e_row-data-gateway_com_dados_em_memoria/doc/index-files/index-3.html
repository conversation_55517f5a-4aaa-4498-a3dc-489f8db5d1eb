<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_77) on Thu Mar 02 07:58:08 WET 2017 -->
<title>G-Index</title>
<meta name="date" content="2017-03-02">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="G-Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="../dataaccess/package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">Prev Letter</a></li>
<li><a href="index-4.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">Frames</a></li>
<li><a href="index-3.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">C</a>&nbsp;<a href="index-2.html">D</a>&nbsp;<a href="index-3.html">G</a>&nbsp;<a href="index-4.html">I</a>&nbsp;<a href="index-5.html">P</a>&nbsp;<a href="index-6.html">R</a>&nbsp;<a href="index-7.html">S</a>&nbsp;<a href="index-8.html">U</a>&nbsp;<a href="index-9.html">V</a>&nbsp;<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="../dataaccess/ConfigurationRowDataGateway.html#getAmountThreshold--">getAmountThreshold()</a></span> - Method in enum dataaccess.<a href="../dataaccess/ConfigurationRowDataGateway.html" title="enum in dataaccess">ConfigurationRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ConfigurationRowDataGateway.html#getAmountThresholdPercentage--">getAmountThresholdPercentage()</a></span> - Method in enum dataaccess.<a href="../dataaccess/ConfigurationRowDataGateway.html" title="enum in dataaccess">ConfigurationRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getClientId--">getClientId()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ConfigurationRowDataGateway.html#getConfiguration--">getConfiguration()</a></span> - Static method in enum dataaccess.<a href="../dataaccess/ConfigurationRowDataGateway.html" title="enum in dataaccess">ConfigurationRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getCustomerById-int-">getCustomerById(int)</a></span> - Static method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>
<div class="block">Fetches a customer given its internal id number and returns a CustomerRowGateway 
 object with the data retrieved from the repository.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getCustomerByVATNumber-int-">getCustomerByVATNumber(int)</a></span> - Static method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>
<div class="block">Fetches a customer given its VAT number and returns a CustomerRowGateway 
 object with the data retrieved from the repository.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getCustomerId--">getCustomerId()</a></span> - Method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getDate--">getDate()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getDescription--">getDescription()</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getDesignation--">getDesignation()</a></span> - Method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getDiscount--">getDiscount()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getDiscountType--">getDiscountType()</a></span> - Method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>
<div class="block">Notice that the setter and the getter converts the integer representing
 the discount id to an enumerated value (of type DiscountType) that is 
 then used in the application's domain logic.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ConfigurationRowDataGateway.html#getEligiblePercentage--">getEligiblePercentage()</a></span> - Method in enum dataaccess.<a href="../dataaccess/ConfigurationRowDataGateway.html" title="enum in dataaccess">ConfigurationRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getFaceValue--">getFaceValue()</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getId--">getId()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getPhoneNumber--">getPhoneNumber()</a></span> - Method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getProdCod--">getProdCod()</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Comment: there is a business rule to not allow product code changes.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getProductByCode-int-">getProductByCode(int)</a></span> - Static method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Finds a product with prodCod and returns its ProductRowGateway, in case
 it exists (Optional).</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getProductById-int-">getProductById(int)</a></span> - Static method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Finds a product given its id and returns its ProductRowGateway.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getProductId--">getProductId()</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>
<div class="block">Comment: the product id is an internal (persistence) concept only used
 to link entities and must not have any business meaning.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#getProductId--">getProductId()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess">SaleProductRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getQty--">getQty()</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#getQty--">getQty()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess">SaleProductRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getSaleById-int-">getSaleById(int)</a></span> - Static method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>
<div class="block">Fetches a sale with a given saleId and returns a sale row gateway object, 
 in case it exists (Optional).</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#getSaleId--">getSaleId()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess">SaleProductRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleProductRowDataGateway.html#getSaleProducts-int-">getSaleProducts(int)</a></span> - Static method in class dataaccess.<a href="../dataaccess/SaleProductRowDataGateway.html" title="class in dataaccess">SaleProductRowDataGateway</a></dt>
<dd>
<div class="block">Obtains the products of a given saleId.</div>
</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getStatus--">getStatus()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/SaleRowDataGateway.html#getTotal--">getTotal()</a></span> - Method in class dataaccess.<a href="../dataaccess/SaleRowDataGateway.html" title="class in dataaccess">SaleRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/ProductRowDataGateway.html#getUnitId--">getUnitId()</a></span> - Method in class dataaccess.<a href="../dataaccess/ProductRowDataGateway.html" title="class in dataaccess">ProductRowDataGateway</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../dataaccess/CustomerRowDataGateway.html#getVAT--">getVAT()</a></span> - Method in class dataaccess.<a href="../dataaccess/CustomerRowDataGateway.html" title="class in dataaccess">CustomerRowDataGateway</a></dt>
<dd>
<div class="block">Comment: There is a business logic decision not to allow changes to the VAT number
 of a customer.</div>
</dd>
</dl>
<a href="index-1.html">C</a>&nbsp;<a href="index-2.html">D</a>&nbsp;<a href="index-3.html">G</a>&nbsp;<a href="index-4.html">I</a>&nbsp;<a href="index-5.html">P</a>&nbsp;<a href="index-6.html">R</a>&nbsp;<a href="index-7.html">S</a>&nbsp;<a href="index-8.html">U</a>&nbsp;<a href="index-9.html">V</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../dataaccess/package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li><a href="../dataaccess/package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">Prev Letter</a></li>
<li><a href="index-4.html">Next Letter</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">Frames</a></li>
<li><a href="index-3.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
