<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>pt.ulisboa.ciencias.di</groupId>
  <artifactId>transaction-script-v0-alunos</artifactId>
  <version>2.0</version>
  <description>An example of the application of the transaction script pattern using row data gateway in-memory maps</description>
  <repositories>
     <repository>
       <id>dataaccess-rdgw</id>
       <name>Data Access RDGW SaleSystem</name>
       <url>file://${project.basedir}/lib/</url>
     </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>pt.ulisboa.ciencias.di</groupId>
      <artifactId>dataaccess-rdgw</artifactId>
      <version>1.0</version>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
