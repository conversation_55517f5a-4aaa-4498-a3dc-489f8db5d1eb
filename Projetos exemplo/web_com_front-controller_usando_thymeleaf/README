***********************************************************
	
		SaleSys @ CSS-LEI-FCUL 
		 
	  Web Presentation + Domain Model + Data Mapper with JPA
		
***********************************************************


***************
	Overview
***************

This project illustrates 
1) the construction of a web application with server-side templates 
2) the application of several patterns:
* MVC (https://martinfowler.com/eaaCatalog/modelViewController.html) and the FrontController design pattern (see https://martinfowler.com/eaaCatalog/frontController.html)  
* the Domain Model design pattern (see https://martinfowler.com/eaaCatalog/domainModel.html) and Data Mapper(see https://martinfowler.com/eaaCatalog/dataMapper.html) via meta-data for JPA

This version of the system needs to deal with concurrent clients (which you can simulate for instance by sending requests from different browsers). Version-based Concurrency Control (a form of optimistic locking) is used.

 
***************
	Use Cases
***************

In this version, we consider there are two use cases: create customer and
process sale. Create customer use case has a single operation to create new customers.
Process sale use case starts with the creation of the sale, after which it is possible to
repeatedly add products to this sale until the sale is closed (ending the use case). 
At any time after the creation  of the sale, it is possible to get the current discount.
 
IMPORTANT: Although the business logic of the use cases is completely implemented,  
the presentation of the process sale use case only covers the first operation.

***************
	Layers
***************

There are four layers: 
* the presentation layer, which contains the code that supports the use of the system by its users through a browser;
* the application layer, which contains the services provided 
by the application and its role (in this version) is just to provide a facade that hides 
from clients our decision of using the domain model pattern; 
* the business layer, which contains the handlers of use cases, the catalogs and the classes representing the domain objects; 
* the data access layer, with code from JPA provider and other artifacts
automatically generated from the provided meta-data. 


***************
	Tiers
***************

This version of the system has 3 tiers:

The client tier, with the web browser running in the client machine.
The backend tier, with the web application running in a machine with a Java Web Server (more precisely, Wildfly).
The data tier, with the database stored in a machine with a database server (more precisely, a MySQL server)

***************
	Persistence
***************

The source of data is a MySQL database in a different machine (DB server available in dbserver.alunos.di.fc.ul.pt). In META-INF (src/main/resources) you can find the file 
ersistence.xml with the required data. 

In order to create the database and, then run the project, do the following:

0. Uncomment the dependency  <groupId>org.hibernate</groupId> in the .pom 
and let maven update the project

1. Run dbutils.CreateDatabase that uses JPA properties together with the annotations 
In the code to create the database. (If something is wrong with the annotations the creation 
will fail. You need to fix the annotations and execute the class again.)

Every time you run this class, the tables from the database will be removed
and the database will be created again. The removal and creation of tables 
is based on the current meta-data, so, if there is a change in the meta-data,
classes can be wrongly deleted (because they were created using annotations
that are no longer in place). In this cases remove (manually) the database
and run the dbutils.CreateDatabase again.

Required data for running the system is also load when running this class.
JPA is given the script in META-INF/load-script.sql for insertion of the data. 

2. Comment again the dependency  <groupId>org.hibernate</groupId> in the .pom 
and let maven update the project again

***************
	Run the web application
***************

In Eclipse, you just need to get the context menu in the root of the project 
and select run as > run on server and then select wildly.
After some seconds, it should show you a browser window with two options. 
Try the first one to create a customer. The drop-down menu should show the 
3 types of discounts SaleSys has. If this is not the case, you can see in the
console that this is because the Discount table was not found. This means that
you need to create the database since it does not exist.
See how to do it in the persistence section above. Once the database is created then 
You just have to ask again to run the application.

Once you have the application running in the server, you can also use your 
favourite web browser and just send a request to 
http://localhost:8080/domain-model-jpa-web-v0/

