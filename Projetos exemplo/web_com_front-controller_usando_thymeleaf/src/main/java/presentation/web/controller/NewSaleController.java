package presentation.web.controller;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.WebContext;

import presentation.web.controller.Controller;
import presentation.web.model.NewSaleModel;

/**
* Handles the newSale event.
* If the request is valid, it dispatches it to the domain model (the application's business logic)
* Notice as well the use of an helper class to assist in the response. 
* 
* <AUTHOR>
* <AUTHOR>
*/

public class NewSaleController extends Controller {

    public NewSaleController() {
        super();
    } 
    
	@Override
	public void process(HttpServletRequest request, HttpServletResponse response, ServletContext servletContext,
			ITemplateEngine templateEngine) throws Exception {

	  	final WebContext ctx = new WebContext(request, response, servletContext, request.getLocale());

	  	NewSaleModel model = new NewSaleModel();
	  	ctx.setVariable("model", model);
	  	
        templateEngine.process("sale/newSale", ctx, response.getWriter());

	}

}
