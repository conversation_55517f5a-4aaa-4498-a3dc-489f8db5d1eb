/*
 * =============================================================================
 * 
 *   Copyright (c) 2011-2016, The THYMELEAF team (http://www.thymeleaf.org)
 * 
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 * 
 *       http://www.apache.org/licenses/LICENSE-2.0
 * 
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 * 
 * =============================================================================
 */
package presentation.web.controller;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.WebContext;

import application.CustomerService;
import facade.exceptions.ApplicationException;
import facade.exceptions.VatInvalidException;
import facade.startup.SaleSys;
import presentation.web.model.NewCustomerModel;

/**
* Handles the createClient event.
* If the request is valid, it dispatches it to the domain model (the application's business logic)
* Notice as well the use of an helper class to assist in the response. 
* 
* @authir fmartins
* <AUTHOR>
*/
public class CreateCustomerController extends Controller {

    
    public CreateCustomerController() {
        super();
    }
    
    
    public void process(
            final HttpServletRequest request, final HttpServletResponse response,
            final ServletContext servletContext, final ITemplateEngine templateEngine)
            throws Exception {
        
        WebContext ctx = new WebContext(request, response, servletContext, request.getLocale());
        
    	SaleSys app = (SaleSys) request.getServletContext().getAttribute("app");
		CustomerService addCustomerService = app.getCustomerService();

		NewCustomerModel model = createModel(request, app);        
		ctx.setVariable("model", model);
		
		if (isInputValid(model)) {
			try {
				addCustomerService.addCustomer(intValue(model.getVATNumber()), 
						model.getDesignation(), intValue(model.getPhoneNumber()), intValue(model.getDiscountType()));
				model.clearFields();
				model.addMessage("Cliente criado com sucesso.");
			} 
			catch (VatInvalidException e) {
				model.addMessage("Erro ao criar cliente, npc inválido: " + e.getMessage());
			}
			catch (ApplicationException e) {
				model.addMessage("Erro ao criar cliente: " + e.getMessage());
			}
		} 
		else {
			model.addMessage("Erro ao validar cliente");
		}
		
        templateEngine.process("customer/newCustomer", ctx, response.getWriter());
       
    }
    
	
	/**
	 * Validate the input request
	 * 
	 * @param nch The model object to be field.
	 * @return True if the fields are inputed correctly
	 */
	private boolean isInputValid(NewCustomerModel nch) {

		// check if designation is filled
		boolean result = isFilled(nch, nch.getDesignation(), "É obrigatório preencher a Designação.");
		
		// check if VATNumber is filled and a valid number
		result &= isFilled(nch, nch.getVATNumber(), "É obrigatório preencher o número de pessoa colectiva")
				 			&& isInt(nch, nch.getVATNumber(), "Número de pessoal colectiva contém caracteres não numéricos");
		
		// check in case phoneNumber is filled if it contains a valid number
		if (!"".equals(nch.getPhoneNumber()))
			result &= isInt(nch, nch.getPhoneNumber(), "Número de telefone contém caracteres não numéricos");

		// check if discount type is filled and a valid number
		result &= isFilled(nch, nch.getDiscountType(), "É obrigatório preencher o Tipo de desconto") 
					&& isInt(nch, nch.getDiscountType(), "Tipo de desconto contém caracteres não numéricos");

		return result;
	}


	/**
	 * Create the customer model object and fills it with data from the request
	 * 
	 * @param request The current HTTP request
	 * @param app The app object
	 * @return The brand new customer model object
	 */
	private NewCustomerModel createModel(HttpServletRequest request, SaleSys app) {
		// Create the object model
		NewCustomerModel model = new NewCustomerModel();
		model.setCustomerService(app.getCustomerService());

		// fill it with data from the request
		model.setDesignation(request.getParameter("designacao"));
		model.setVATNumber(request.getParameter("npc"));
		model.setPhoneNumber(request.getParameter("telefone"));
		model.setDiscountType(request.getParameter("desconto"));
		
		return model;
	}	

}
