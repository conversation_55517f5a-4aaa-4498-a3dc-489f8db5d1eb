package presentation.web.controller;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.WebContext;

import application.SaleService;
import facade.exceptions.ApplicationException;
import facade.startup.SaleSys;
import presentation.web.controller.Controller;
import presentation.web.model.NewSaleModel;

/**
 * Handles the createSale event.
 * If the request is valid, it dispatches it to the domain model (the application's business logic)
 * Notice as well the use of an helper class to assist in the response. 
 * 
 * @authir fmartins
 * <AUTHOR>
 *
 */

public class CreateSaleController extends Controller {

	@Override
	public void process(HttpServletRequest request, HttpServletResponse response, ServletContext servletContext,
			ITemplateEngine templateEngine) throws Exception {

		WebContext ctx = new WebContext(request, response, servletContext, request.getLocale());


		SaleSys app = (SaleSys) request.getServletContext().getAttribute("app");
		SaleService saleService = app.getSaleService();
		ctx.setVariable("saleService", saleService);

		NewSaleModel model = createModel(request,saleService);
		ctx.setVariable("model", model);

		if (isInputValid(model)) {
			try {
				saleService.newSale(intValue(model.getVATNumber())); 
				model.clearFields();
				model.addMessage("Venda criada com sucesso.");
			} catch (ApplicationException e) {
				model.addMessage("Erro ao criar venda: " + e.getMessage());
			}
		} 
		else {
			model.addMessage("Erro ao validar venda");
		}	

		templateEngine.process("sale/newSale", ctx, response.getWriter());
	}

	/**
	 * Validate the input request
	 * 
	 * @param nch The model object to be field.
	 * @return True if the fields are inputed correctly
	 */	
	private boolean isInputValid(NewSaleModel nvh) {		
		// check if VATNumber is filled and a valid number
		return isFilled(nvh, nvh.getVATNumber(), "É obrigatório preencher o número de pessoa colectiva")
				&& isInt(nvh, nvh.getVATNumber(), "Número de pessoal colectiva contém caracteres não numéricos");
	}	

	/**
	 * Create the sale model object and fills it with data from the request
	 * 
	 * @param request The current HTTP request
	 * @param saleService 
	 * @param app The app object
	 * @return The brand new sale model object
	 */
	private NewSaleModel createModel(HttpServletRequest request, SaleService saleService) {
		// Create the object model
		NewSaleModel model = new NewSaleModel();

		// fill it with data from the request
		model.setVATNumber(request.getParameter("npc"));
		// fill the sale service used to the servve this sale
		model.setCurrentSaleService(saleService);
		return model;
	}	

}
