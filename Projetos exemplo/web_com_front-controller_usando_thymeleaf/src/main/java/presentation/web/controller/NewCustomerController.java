/*
 * =============================================================================
 * 
 *   Copyright (c) 2011-2016, The THYMELEAF team (http://www.thymeleaf.org)
 * 
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 * 
 *       http://www.apache.org/licenses/LICENSE-2.0
 * 
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 * 
 * =============================================================================
 */
package presentation.web.controller;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.WebContext;

import facade.startup.SaleSys;
import presentation.web.model.NewCustomerModel;

/**
* Handles the newCustomer event.
* If the request is valid, it dispatches it to the domain model (the application's business logic)
* Notice as well the use of an helper class to assist in the response. 
* 
* <AUTHOR>
* <AUTHOR>
*/

public class NewCustomerController extends Controller {

    public NewCustomerController() {
        super();
    } 
    
    public void process(
            final HttpServletRequest request, final HttpServletResponse response,
            final ServletContext servletContext, final ITemplateEngine templateEngine)
            throws Exception {

    	final WebContext ctx = new WebContext(request, response, servletContext, request.getLocale());

		SaleSys app = (SaleSys) request.getServletContext().getAttribute("app");

		NewCustomerModel model = new NewCustomerModel();
		model.setCustomerService(app.getCustomerService());
		ctx.setVariable("model", model);
		                
        templateEngine.process("customer/newCustomer", ctx, response.getWriter());
    }

}
