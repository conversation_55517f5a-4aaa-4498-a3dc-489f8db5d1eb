package presentation.web.model;

import java.util.LinkedList;

import application.CustomerService;
import facade.exceptions.ApplicationException;
import facade.interfaces.IDiscount;

/**
 * Helper class to assist in the response to the new customer resquest.
 * This class is the response information expert.
 * 
 * <AUTHOR>
 *
 */
public class NewCustomerModel extends Model {

	private static final long serialVersionUID = 1L;
	
	private String designation;
	private String vatNumber;
	private String phoneNumber;
	private String discountType;
	
	public void setDesignation(String designation) {
		this.designation = designation;	
	}
	
	public String getDesignation() {
		return designation;
	}
	
	public void setVATNumber(String vatNumber) {
		this.vatNumber = vatNumber;
	}
	
	public String getVATNumber() {
		return vatNumber;
	}
	

	
	public String getPhoneNumber() {
		return phoneNumber;
	}
	
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public void setDiscountType(String discountType) {
		this.discountType = discountType;
	}

	public String getDiscountType() {
		return discountType;
	}
	
	public void clearFields() {
		designation = vatNumber = phoneNumber = "";
		discountType = "1";
	}
	
	private CustomerService customerService;
	
	public void setCustomerService(CustomerService customerService) {
		this.customerService = customerService;
	}
	
	public Iterable<IDiscount> getDiscounts () {
		try {
			return customerService.getDiscounts();
		} catch (ApplicationException e) {
			return new LinkedList<> ();		
		}
	}	
}
