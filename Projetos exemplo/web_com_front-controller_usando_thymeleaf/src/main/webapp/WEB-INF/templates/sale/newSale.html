<!DOCTYPE html>

<html xmlns:th="http://www.thymeleaf.org">

<head>
<title>SaleSys</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<link rel="stylesheet" type="text/css" media="all"
	href="../../../css/app.css" th:href="@{/css/app.css}" />
</head>

<body>

	<h1>Efectuar Venda</h1>

	<form action="createSale" th:action="@{/sale/create}">
		<fieldset>
			<legend>Venda</legend>
			<div class="mandatory_field">
				<label for="npc">Número de pessoa colectiva:</label> <input
					type="text" name="npc" value="o número de pessoa colectiva"
					th:value="${model.VATNumber}" /> <br />
			</div>
			<p>Venda Corrente</p>
			<p>Designação cliente: <span th:text="${model.designation}">designação</span>.
			</p>
			<div class="button" align="right">
				<input type="submit" value="Criar Venda">
			</div>
		</fieldset>
	</form>


	<div th:if="${model.hasMessages}">
		<h2>Mensagens</h2>
		<ul th:each="msg: ${model.messages}">
			<li th:text="${msg}">mensagem</li>
		</ul>
	</div>

	<p>
		<a href="home.html" th:href="@{/}">Return to home</a>
	</p>
</body>

</html>

