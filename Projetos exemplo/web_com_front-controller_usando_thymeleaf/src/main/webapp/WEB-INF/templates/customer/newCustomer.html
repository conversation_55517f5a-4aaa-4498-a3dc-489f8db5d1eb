<!DOCTYPE html>

<html xmlns:th="http://www.thymeleaf.org">

<head>
<title>SaleSys</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<link rel="stylesheet" type="text/css" media="all"
	href="../../../css/app.css" th:href="@{/css/app.css}" />
</head>

<body>

	<h1>Adicionar cliente</h1>

	<form action="createClient" th:action="@{/customer/create}">
		<div class="mandatory_field">
			<label for="designacao">Designação:</label> <input type="text"
				size="50" name="designacao" value="a designação"
				th:value="${model.designation}" />
		</div>
		<div class="mandatory_field">
			<label for="npc">Número de pessoa colectiva:</label> <input
				type="text" size="10" name="npc"
				value="o número de pessoa colectiva" th:value="${model.VATNumber}" />
		</div>
		<div class="optional_field">
			<label for="telefone">Telefone:</label> <input type="text" size="10"
				value="o telefone" name="telefone" th:value="${model.phoneNumber}" />
		</div>
		<div class="mandatory_field">
			<label for="desconto">Tipo de desconto:</label> 
			<select name="desconto">
				<option value="">---</option>
				<option th:each="desconto: ${model.discounts}"  
						th:value="${desconto.id}" 
						th:text="${desconto.description}"
						th:selected="${desconto.id == model.discountType}"> opção
				</option>
			</select>
		</div>         
		<div class="button" align="right">
			<input type="submit" value="Criar Cliente">
		</div>
	</form>


	<div th:if="${model.hasMessages}">
	<h2>Mensagens</h2>
		<ul th:each="msg: ${model.messages}">
			<li th:text="${msg}">mensagem</li>
		</ul>
	</div>

	<p>
		<a href="home.html" th:href="@{/}">Return to home</a>
	</p>
</body>

</html>

